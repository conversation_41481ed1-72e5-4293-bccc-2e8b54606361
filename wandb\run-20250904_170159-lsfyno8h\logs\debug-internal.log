{"time":"2025-09-04T17:02:00.0353078+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-09-04T17:02:01.2453834+08:00","level":"INFO","msg":"stream: created new stream","id":"lsfyno8h"}
{"time":"2025-09-04T17:02:01.2453834+08:00","level":"INFO","msg":"stream: started","id":"lsfyno8h"}
{"time":"2025-09-04T17:02:01.2459806+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"lsfyno8h"}
{"time":"2025-09-04T17:02:01.2453834+08:00","level":"INFO","msg":"handler: started","stream_id":"lsfyno8h"}
{"time":"2025-09-04T17:02:01.2453834+08:00","level":"INFO","msg":"sender: started","stream_id":"lsfyno8h"}
{"time":"2025-09-04T17:02:01.739104+08:00","level":"ERROR","msg":"git repo not found","error":"repository does not exist"}
{"time":"2025-09-04T17:42:01.7389338+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:42:16.7382726+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:42:31.738385+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:42:46.7385626+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:43:01.7385846+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:43:16.7383382+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:43:31.7383949+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:43:46.7385968+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:44:01.738081+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:44:16.7384749+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:44:31.7381365+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:44:46.7385572+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:45:01.7384224+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:45:16.738328+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:45:31.7381981+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:45:46.7385209+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:46:01.7383267+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:46:16.7383779+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:46:31.7385052+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:46:46.7384057+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:47:01.7386646+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:47:16.738243+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:47:31.7385376+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:47:46.7382206+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:48:01.7383274+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:48:16.738098+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:48:31.7384078+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:48:46.7382291+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:49:01.7384022+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:49:16.7383937+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:49:31.7384975+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:49:46.7381017+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:50:01.7384234+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:50:16.7386592+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:50:31.7383825+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:50:46.7384915+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:51:01.73857+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:51:16.7382885+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:51:31.7387688+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:51:46.7383998+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:52:01.7385274+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:52:16.738478+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:52:31.7386679+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:52:46.738301+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:53:01.7385461+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:53:16.7386473+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:53:31.7387436+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:53:46.7382861+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:54:01.7384422+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:54:16.7384405+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:54:31.7382641+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:54:46.7384803+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:55:01.7383797+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:55:16.7383789+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:55:31.7386641+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:55:46.7384536+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:56:01.738427+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:56:16.7383234+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:56:31.7385508+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:56:46.7386446+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:57:01.7385028+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:57:16.738577+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:57:31.7383605+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:57:46.7382899+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:58:01.7384171+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:58:16.7384866+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:58:31.7382619+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:58:46.7387492+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:59:01.7386512+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:59:16.7384748+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:59:31.7381874+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T17:59:46.738753+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:00:01.7385717+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:00:16.7384391+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:00:31.7384122+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:00:46.7385404+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:01:01.7381949+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:01:16.7381822+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:01:31.7383396+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:01:46.7382486+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:02:01.738254+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:02:16.7384831+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:02:31.7384191+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:02:46.7386785+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:03:01.7383882+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:03:16.7383297+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:03:31.7386042+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:03:46.7383763+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:04:01.738462+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:04:16.7384019+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:04:31.7382583+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:04:46.7383369+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:05:01.7385493+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:05:16.738271+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:05:31.7385644+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:05:46.7383821+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:06:01.7382608+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:06:16.7381705+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:06:31.7386764+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:06:46.7387291+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:07:01.7380945+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:07:16.7384519+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:07:31.738529+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:07:46.7386646+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:08:01.7384256+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:08:16.7387076+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:08:31.7385992+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:08:46.7382322+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:09:01.7382271+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:09:16.7384546+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:09:31.738553+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:09:46.7385163+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:10:01.738551+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:10:16.7381735+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:10:31.738342+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:10:46.7384409+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:11:01.7384989+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:11:16.7385115+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:11:31.7382649+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:11:46.7385655+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:12:01.7384186+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:12:16.7385912+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:12:31.7384618+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:12:46.7384639+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:13:01.7382984+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:13:16.7389702+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:13:31.7381536+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:13:46.7386759+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:14:01.7385644+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:14:16.7384774+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:14:31.7382287+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:14:46.7383957+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:15:01.7385632+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:15:16.7383547+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:15:31.7384337+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:15:46.7384771+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:16:01.7390804+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:16:16.7386013+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:16:31.7382394+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:16:46.7386128+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:17:01.7382714+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:17:16.7384243+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:17:31.7385099+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:17:46.7387344+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:18:01.7384728+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:18:16.7383226+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:18:31.7389512+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:18:46.7382529+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:19:01.7388132+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:19:16.738357+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:19:31.7402269+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:19:46.7381561+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:20:01.7383055+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:20:16.7387112+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:20:31.7383267+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:20:46.7389207+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:21:01.7384352+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:21:16.7383638+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:21:31.7383377+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:21:46.7383815+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:22:01.7394205+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:22:16.7387954+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:22:31.7385724+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:22:46.7387701+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:23:01.7382199+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:23:16.7385035+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:23:31.738749+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:23:46.7382045+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:24:01.7384826+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:24:16.73862+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:24:31.738667+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:24:46.7383789+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:25:01.7387076+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:25:16.7383431+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:25:31.7386225+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:25:46.7385942+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:26:01.7385478+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:26:16.7383732+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:26:31.7391857+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:26:46.7385074+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:27:01.7382498+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:27:16.7383859+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:27:31.7385018+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:27:46.7384552+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:28:01.7385538+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:28:16.7388727+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:28:31.7384823+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:28:46.7382667+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:29:01.7383812+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:29:16.7387501+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:29:31.7384535+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:29:46.7387066+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:30:01.7383163+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:30:16.7383004+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:30:31.7386991+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:30:46.7385496+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:31:01.7383618+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:31:16.7384474+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:31:31.7383221+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:31:46.7384923+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:32:01.7389293+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:32:16.7384457+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:32:31.7382969+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:32:46.7383624+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:33:01.738685+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:33:16.7383016+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:33:31.7385784+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:33:46.7386215+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:34:01.7385885+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:34:16.7386436+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:34:31.7383423+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:34:46.7387128+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:35:01.7383855+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:35:16.7385889+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:35:31.7388354+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:35:46.738224+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:36:01.7384063+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:36:16.738677+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:36:31.7384721+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:36:46.7384681+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:37:01.738233+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:37:16.7384354+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:37:31.7385145+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:37:46.7384817+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:38:01.738502+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:38:16.7383498+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:38:31.7381459+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:38:46.7387853+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:39:01.7386878+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:39:16.7382449+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:39:31.7382345+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:39:46.7383721+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:40:01.7386251+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:40:16.7386618+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:40:31.7384755+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:40:46.7383804+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:41:01.7385106+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:41:16.738477+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:41:31.7384166+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:41:46.7384847+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:42:01.7383483+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:42:16.7381396+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:42:31.7381146+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:42:46.7381898+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:43:01.7382808+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:43:16.73885+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:43:31.7388391+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:43:46.7380729+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:44:01.7383612+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:44:16.7385117+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:44:31.7392732+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:44:46.73842+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:45:01.7386288+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:45:16.7383173+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:45:31.7385912+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:45:46.7385782+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:46:01.738332+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:46:16.7380875+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:46:31.7383871+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:46:46.738173+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:47:01.738444+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:47:16.7381731+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:47:31.7385129+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:47:46.7384956+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:48:01.738143+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:48:16.7381115+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:48:31.7384485+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:48:46.7383006+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:49:01.7384554+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:49:16.7384454+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:49:31.7382853+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:49:46.7381449+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:50:01.7381553+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:50:16.7385558+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:50:31.7384824+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:50:46.738247+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:51:01.7384841+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:51:16.738549+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:51:31.7385053+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:51:46.7384615+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:52:01.7384202+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:52:16.7384563+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:52:31.7381009+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:52:46.7383605+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:53:01.7384882+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:53:16.738597+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:53:31.7384943+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:53:46.7384134+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:54:01.7382789+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:54:16.7381668+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:54:31.7384616+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:54:46.7383334+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:55:01.7385946+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:55:16.7381588+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:55:31.7383644+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:55:46.7384177+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:56:01.7382068+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:56:16.738655+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:56:31.7385579+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:56:46.7382864+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:57:01.7385473+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:57:16.7384691+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:57:31.7385011+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:57:46.7384874+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:58:01.7384657+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:58:16.738551+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:58:31.7382758+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:58:46.7382934+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:59:01.7384051+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:59:16.7382782+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:59:31.7385277+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T18:59:46.7383906+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:00:01.7382003+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:00:16.7385175+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:00:31.7384185+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:00:46.7383975+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:01:01.7387333+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:01:16.7385504+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:01:31.7383694+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:01:46.7386704+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:02:01.7384891+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:02:16.7385421+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:02:31.7382508+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:02:46.7385032+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:03:01.7383314+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:03:16.7384656+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:03:31.7384896+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:03:46.7383422+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:04:01.7381558+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:04:16.7384+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:04:31.7386712+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:04:46.7383618+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:05:01.738695+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:05:16.7381591+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:05:31.7384023+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:05:46.7385674+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:06:01.7381369+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:06:16.7382517+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:06:31.7385329+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:06:46.738324+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:07:01.7385017+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:07:16.7385834+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:07:31.7382982+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:07:46.7384755+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:08:01.7383182+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:08:16.7385306+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:08:31.7385941+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:08:46.7385428+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:09:01.7385036+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:09:16.7386137+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:09:31.7385747+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:09:46.7383642+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:10:01.7384618+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:10:16.7384093+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:10:31.7383396+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:10:46.7385843+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:11:01.7383717+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:11:16.7383712+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:11:31.7383523+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:11:46.7384609+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:12:01.7385473+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:12:16.7383188+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:12:31.7381851+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:12:46.7385064+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:13:01.7386098+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:13:16.7385397+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:13:31.7387198+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:13:46.7383242+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:14:01.7384942+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:14:16.7384282+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:14:31.7384287+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:14:46.7384149+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:15:01.738343+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:15:16.7384599+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:15:31.7382289+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:15:46.7385634+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:16:01.7383418+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:16:16.7381969+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:16:31.7384124+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:16:46.7386518+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:17:01.7383262+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:17:16.73851+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:17:31.7382602+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:17:46.738442+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:18:01.7382136+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:18:16.7384996+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:18:31.7383084+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:18:46.738387+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:19:01.7383773+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:19:16.73867+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:19:31.7383617+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:19:46.7381825+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:20:01.7383198+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:20:16.7385297+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:20:31.7385044+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:20:46.7381257+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:21:01.7386359+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:21:16.7383852+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:21:31.7382827+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:21:46.7382167+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:22:01.7381023+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:22:16.7383356+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:22:31.7384638+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:22:46.7384183+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:23:01.7384887+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:23:16.7381856+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:23:31.7382948+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:23:46.7383062+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:24:01.7384581+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:24:16.7382572+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:24:31.7382457+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:24:46.7384742+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:25:01.7382564+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:25:16.738219+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:25:31.7385594+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:25:46.7383007+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:26:01.7381038+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:26:16.7382725+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:26:31.7383251+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:26:46.7381821+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:27:01.738319+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:27:16.7383107+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:27:31.7383565+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:27:46.7381591+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:28:01.7385901+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:28:16.7386015+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:28:31.7382313+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:28:46.738282+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:29:01.7385538+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:29:16.7384797+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:29:31.7381313+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:29:46.7383448+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:30:01.7382499+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:30:16.7385713+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:30:31.7384135+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:30:46.7383196+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:31:01.738339+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:31:16.7381215+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:31:31.7383168+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:31:46.738397+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:32:01.7385618+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:32:16.7385709+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:32:31.7385447+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:32:46.7383373+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:33:01.7385406+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:33:16.7384578+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:33:31.7383869+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:33:46.738265+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:34:01.7386519+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:34:16.7384659+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:34:31.7381308+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:34:46.7384738+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:35:01.7384055+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:35:16.7383769+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:35:31.7384494+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:35:46.7384468+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:36:01.7382968+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:36:16.7383421+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:36:31.7385464+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:36:46.7381413+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:37:01.7381536+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:37:16.73821+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:37:31.7381488+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:37:46.7383718+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:38:01.7381748+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:38:16.7382907+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:38:31.7381478+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:38:46.7382436+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:39:01.7382407+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:39:16.7382738+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:39:31.7381689+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:39:46.738258+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:40:01.7381258+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:40:16.7380975+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:40:31.7384294+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:40:46.7384377+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:41:01.738419+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:41:16.7381999+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:41:31.7381397+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:41:46.7383426+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:42:01.7385733+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:42:16.7382808+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:42:31.7385266+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:42:46.738324+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:43:01.7382188+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:43:16.7384383+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:43:31.7385561+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:43:46.738295+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:44:01.7382549+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:44:16.7382824+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:44:31.7384129+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:44:46.73814+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:45:01.7381144+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:45:16.738647+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:45:31.7383021+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:45:46.738389+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:46:01.7382091+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:46:16.7382979+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:46:31.738336+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:46:46.7382196+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:47:01.7383691+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:47:16.7385063+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:47:31.7385784+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:47:46.7381467+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:48:01.7382301+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:48:16.7382236+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:48:31.738527+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:48:46.7382439+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:49:01.7385733+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:49:16.7383365+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:49:31.7384579+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:49:46.7384256+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:50:01.7380949+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:50:16.7381596+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:50:31.7386204+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:50:46.7385279+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:51:01.7385141+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:51:16.7381915+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:51:31.7381593+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:51:46.7385259+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:52:01.7385075+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:52:16.7384666+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:52:31.7384516+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:52:46.738312+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:53:01.7383284+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:53:16.738171+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:53:31.7382364+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:53:46.7380887+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:54:01.7383246+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:54:16.7384174+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:54:31.7383103+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:54:46.7384728+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:55:01.7382097+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:55:16.7382084+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:55:31.738319+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:55:46.7383208+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:56:01.7385562+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:56:16.7385101+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:56:31.7383921+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:56:46.7385636+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:57:01.7381867+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:57:16.7381238+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:57:31.7383518+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:57:46.7382704+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:58:01.7383346+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:58:16.738303+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:58:31.7382248+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:58:46.7384313+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:59:01.7382774+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:59:16.7382086+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:59:31.7384173+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T19:59:46.7381539+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:00:01.73826+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:00:16.7381458+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:00:31.7382073+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:00:46.7383949+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:01:01.7384293+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:01:16.7383508+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:01:31.7385864+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:01:46.7381031+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:02:01.7382805+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:02:16.7382725+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:02:31.7381034+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:02:46.7385385+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:03:01.7384279+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:03:16.7383609+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:03:31.7382102+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:03:46.7384647+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:04:01.7382083+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:04:16.7385473+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:04:31.7382155+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:04:46.7382881+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:05:01.7385574+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:05:16.7384731+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:05:31.7385192+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:05:46.7385094+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:06:01.7382739+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:06:16.7382852+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:06:31.7385399+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:06:46.7385252+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:07:01.7381731+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:07:16.7381408+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:07:31.7381502+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:07:46.7383602+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:08:01.7381746+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:08:16.7384247+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:08:31.7384461+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:08:46.7385414+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:09:01.7384401+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:09:16.738373+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:09:31.7385467+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:09:46.7384605+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:10:01.7382528+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:10:16.7384038+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:10:31.7380951+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:10:46.7383217+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:11:01.7382379+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:11:16.7385583+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:11:31.7384963+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:11:46.7383642+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:12:01.7385854+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:12:16.7382893+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:12:31.7382324+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:12:46.7384957+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:13:01.7384119+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:13:16.7382671+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:13:31.7383576+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:13:46.7385744+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:14:01.7384407+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:14:16.7384709+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:14:31.7384921+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:14:46.7384707+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:15:01.7384606+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:15:16.7382019+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:15:31.7382018+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:15:46.73822+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:16:01.7382473+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:16:16.7381711+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:16:31.738337+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:16:46.7384529+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:17:01.7384773+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:17:16.7382384+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:17:31.7384388+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:17:46.7382938+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:18:01.7382091+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:18:16.7385673+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:18:31.7390515+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:18:46.7381512+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:19:01.73825+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:19:16.7382111+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:19:31.7382897+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:19:46.7382104+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:20:01.7383035+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:20:16.7384839+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:20:31.7383295+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:20:46.738511+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:21:01.7383271+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:21:16.7382032+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:21:31.738453+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:21:46.7382768+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:22:01.7383789+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:22:16.7381946+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:22:31.7383043+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:22:46.7384252+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:23:01.7384768+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:23:16.7381676+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:23:31.7380862+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:23:46.7385439+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:24:01.7384769+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:24:16.7383603+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:24:31.7384109+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:24:46.7382519+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:25:01.7388096+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:25:16.7383233+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:25:31.7383349+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:25:46.7386169+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:26:01.7386764+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:26:16.7384851+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:26:31.7384466+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:26:46.7381253+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:27:01.7385269+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:27:16.7382398+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:27:31.7384525+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:27:46.7385275+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:28:01.7383916+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:28:16.7381324+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:28:31.738459+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:28:46.7383804+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:29:01.7383274+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:29:16.7384875+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:29:31.7383392+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:29:46.7383264+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:30:01.7384316+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:30:16.7388696+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:30:31.7381912+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:30:46.7381904+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:31:01.7385997+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:31:16.7384282+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:31:31.7383866+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:31:46.7381741+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:32:01.7383993+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:32:16.738565+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:32:31.7381428+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:32:46.7382715+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:33:01.7382133+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:33:16.7382382+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:33:31.7385013+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:33:46.7385363+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:34:01.7388313+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:34:16.7385014+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:34:31.7381617+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:34:46.7385168+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:35:01.7385628+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:35:16.7384264+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:35:31.7383098+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:35:46.7384729+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:36:01.7384651+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:36:16.7383203+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:36:31.7381617+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:36:46.7383824+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:37:01.7381178+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:37:16.7384741+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:37:31.7380843+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:37:46.7382483+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:38:01.7380813+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:38:16.7386056+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:38:31.738354+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:38:46.7382719+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:39:01.7382705+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:39:16.7383046+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:39:31.7382223+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:39:46.73814+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:40:01.7381931+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:40:16.7381572+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:40:31.7385179+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:40:46.7385601+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:41:01.738131+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:41:16.7385737+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:41:31.7384248+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:41:46.7382223+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:42:01.7382416+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:42:16.7386067+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:42:31.7384157+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:42:46.7384681+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:43:01.7385099+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:43:16.738154+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:43:31.7384498+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:43:46.7383899+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:44:01.7385126+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:44:16.738533+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:44:31.7381127+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:44:46.7380872+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:45:01.7381371+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:45:16.7383187+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:45:31.7382684+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:45:46.7381497+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:46:01.7381402+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:46:16.7382862+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:46:31.7384144+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:46:46.7383534+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:47:01.738641+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:47:16.7383093+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:47:31.7385561+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:47:46.7385428+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:48:01.7381112+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:48:16.7383491+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:48:31.7384133+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:48:46.7385629+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:49:01.7381357+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:49:16.7382243+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:49:31.7381406+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:49:46.7382699+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:50:01.7385679+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:50:16.738582+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:50:31.7385121+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:50:46.7385639+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:51:01.7381518+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:51:16.7381968+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:51:31.7383984+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:51:46.7384503+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:52:01.7381062+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:52:16.7384407+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:52:31.73811+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:52:46.7383661+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:53:01.7384554+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:53:16.7384417+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:53:31.7384343+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:53:46.7383819+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:54:01.7383583+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:54:16.7381989+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:54:31.7382643+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:54:46.7385702+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:55:01.7380538+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:55:16.7386302+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:55:31.7383851+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:55:46.7387236+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:56:01.7385273+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:56:16.7385346+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:56:31.7386122+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:56:46.7382453+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:57:01.7383104+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:57:16.7385966+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:57:31.7385732+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:57:46.7382543+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:58:01.7383071+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:58:16.7384181+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:58:31.738124+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:58:46.7380738+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:59:01.7382719+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:59:16.7385327+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:59:31.7383086+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T20:59:46.738235+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:00:01.7388122+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:00:16.7385617+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:00:31.7382729+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:00:46.7387042+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:01:01.7381814+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:01:16.738286+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:01:31.7381662+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:01:46.7381431+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:02:01.7381837+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:02:16.7383832+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:02:31.7383997+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:02:46.7382428+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:03:01.7382754+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:03:16.7384213+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:03:31.7381972+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:03:46.738296+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:04:01.7382273+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:04:16.7383492+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:04:31.7388297+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:04:46.7383869+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:05:01.7382993+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:05:16.7384867+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:05:31.7381488+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:05:46.7385554+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:06:01.7383001+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:06:16.7382537+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:06:31.7389012+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:06:46.7382148+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:07:01.7381769+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:07:16.7383682+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:07:31.7381341+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:07:46.7383134+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:08:01.7383099+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:08:16.7385773+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:08:31.7385201+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:08:46.7382916+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:09:01.7382226+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:09:16.7383277+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:09:31.7382852+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:09:46.7383987+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:10:01.7382459+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:10:16.7385448+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:10:31.7383812+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:10:46.7384034+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:11:01.7384148+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:11:16.738274+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:11:31.7382764+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:11:46.7381594+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:12:01.7386545+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:12:16.7383264+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:12:31.7381454+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:12:46.7383161+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:13:01.7385396+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:13:16.7381533+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:13:31.7381217+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:13:46.7381875+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:14:01.7381048+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:14:16.7382929+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:14:31.7381843+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:14:46.7386066+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:15:01.7385826+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:15:16.7381886+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:15:31.7385157+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:15:46.7382793+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:16:01.7383895+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:16:16.7388105+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:16:31.7380752+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:16:46.7387142+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:17:01.7384864+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:17:16.7384697+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:17:31.7381162+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:17:46.7381889+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:18:01.7381588+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:18:16.7384698+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:18:31.7385199+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:18:46.7385625+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:19:01.7381146+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:19:16.7383575+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:19:31.7385676+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:19:46.7387775+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:20:01.7387926+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:20:16.738205+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:20:31.7382407+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:20:46.7382832+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:21:01.7382954+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:21:16.7383411+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:21:31.7382671+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:21:46.7381048+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:22:01.738553+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:22:16.7382007+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:22:31.7386743+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:22:46.7385655+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:23:01.7386865+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:23:16.7384357+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:23:31.7385867+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:23:46.7387255+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:24:01.7383186+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:24:16.7381741+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:24:31.7385218+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:24:46.7381022+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:25:01.7382608+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:25:16.7382191+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:25:31.7385135+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:25:46.7381165+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:26:01.7382075+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:26:16.7385047+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:26:31.7384648+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:26:46.7384298+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:27:01.7385896+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:27:16.7380959+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:27:31.7381233+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:27:46.738466+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:28:01.7382809+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:28:16.7385592+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:28:31.7383809+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:28:46.7383888+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:29:01.7384182+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:29:16.7380633+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:29:31.7383028+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:29:46.7381622+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:30:01.7381966+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:30:16.7384558+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:30:31.738909+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:30:46.7382387+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:31:01.7382926+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:31:16.7380521+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:31:31.7382593+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:31:46.7383276+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:32:01.738413+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:32:16.7384558+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:32:22.8619552+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": EOF"}
{"time":"2025-09-04T21:32:31.7381433+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:32:46.738184+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:33:01.738391+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:33:16.7385368+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:33:31.7384554+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:33:46.7384291+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:34:01.7384379+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:34:16.7381923+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:34:31.7384021+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:34:46.7385866+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:35:01.7384848+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:35:16.7382733+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:35:31.7382659+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:35:46.7385994+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:36:01.7382318+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:36:16.7392131+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:36:31.7381072+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:36:46.7382531+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:37:01.7381615+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:37:16.7386457+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:37:31.7383687+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:37:46.7385709+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:38:01.7387329+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:38:16.7382505+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:38:31.7381321+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:38:46.7382014+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:39:01.7384162+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:39:16.7383821+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:39:31.7385779+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:39:46.7383106+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:40:01.7383109+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:40:16.7382985+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:40:31.7384594+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:40:46.7382196+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:41:01.7382273+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:41:16.738249+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:41:31.7385724+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:41:46.738713+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:42:01.7384504+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:42:16.7384309+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:42:31.7383342+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:42:46.7381535+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:43:01.7385831+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:43:16.7385939+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:43:31.7384486+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:43:46.7381311+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:44:01.7383085+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:44:16.7382078+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:44:31.7385422+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:44:46.7383299+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:45:01.7385232+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:45:16.7381002+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:45:31.7382832+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:45:46.7384782+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:46:01.7386323+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:46:16.7382319+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:46:31.7380637+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:46:46.7384307+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:47:01.7385152+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:47:16.7384932+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:47:31.738414+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:47:46.7385606+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:48:01.7381142+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:48:16.7380676+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:48:31.7385523+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:48:46.7385667+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:49:01.7384857+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:49:16.7384482+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:49:31.7385729+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:49:46.7384817+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:49:52.7319776+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": EOF"}
{"time":"2025-09-04T21:50:01.7383871+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:50:16.7381961+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:50:31.7383928+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:50:46.7384495+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:51:01.7381862+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:51:16.7384261+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:51:31.7382778+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:51:46.7385558+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:52:01.738556+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:52:16.7385826+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:52:31.7385024+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:52:46.7380856+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:53:01.7382787+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:53:16.738208+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:53:31.7381215+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:53:38.7755691+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": EOF"}
{"time":"2025-09-04T21:53:46.7384813+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:54:01.7381745+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:54:16.7382804+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:54:31.7382898+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:54:46.7381815+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:55:01.7385612+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:55:16.7384967+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:55:31.7384605+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:55:46.738459+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:56:01.7386274+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:56:16.738359+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:56:31.7385323+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:56:46.7382482+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:57:01.7381795+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:57:16.7383466+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:57:31.7381141+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:57:46.7384302+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:58:01.7383958+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:58:16.7384596+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:58:31.7385648+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:58:46.7384655+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:59:01.738273+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:59:16.7385755+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:59:31.7386544+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T21:59:46.7381219+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:00:01.7381376+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:00:16.7382224+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:00:31.7385052+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:00:46.7382276+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:01:01.7381829+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:01:16.738614+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:01:31.7382649+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:01:46.7382354+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:02:01.7384199+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:02:18.8011799+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:02:33.8011914+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:02:48.8014565+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:03:03.8011574+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:03:18.8012409+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:03:33.8014407+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:03:48.80155+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:04:03.8013873+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:04:18.8014358+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:04:33.8010823+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:04:48.801279+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:05:03.80106+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:05:18.8011038+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:05:33.8013946+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:05:48.8012816+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:06:03.8011978+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:06:18.8014722+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:06:33.8011541+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:06:48.8013893+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:07:03.8015263+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:07:18.8013722+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:07:33.8014822+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:07:48.8012471+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:08:03.8010703+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:08:18.8011669+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:08:33.8010728+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:08:48.8010934+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:09:03.8010584+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:09:18.801029+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:09:33.8014602+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:09:48.8013851+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:10:03.8011254+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:10:18.8011842+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:10:33.8011562+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:10:48.80121+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:11:03.8013057+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:11:18.8011317+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:11:33.8012137+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:11:48.8014389+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:12:03.8013365+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:12:18.8010886+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:12:33.8015824+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:12:48.8013528+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:13:03.8011457+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:13:18.8012095+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:13:33.8013809+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:13:48.8011521+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:14:03.8013254+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:14:18.8013315+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:14:33.8015201+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:14:48.8014336+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:15:03.8012212+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:15:18.8014046+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:15:33.8011601+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:15:48.801326+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:16:03.801461+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:16:09.9401367+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": EOF"}
{"time":"2025-09-04T22:16:18.8014583+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:16:33.8013203+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:16:48.8012376+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:17:03.8011971+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:17:18.8011799+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:17:33.8010512+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:17:48.8013722+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:18:03.8014877+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:18:18.8015039+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:18:33.8011965+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:18:48.8011125+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:19:03.8011162+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:19:18.8013558+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:19:33.8012354+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:19:48.8014155+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:20:03.8013698+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:20:18.8014177+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:20:33.8010399+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:20:48.8010167+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:21:03.8014354+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:21:18.8014906+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:21:33.8010908+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:21:48.8014643+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:22:03.8013314+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:22:18.8013637+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:22:33.8013587+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:22:48.8012534+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:23:03.8012845+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:23:18.8015414+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:23:33.8014044+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:23:48.8011512+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:24:03.8014837+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:24:18.8012001+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:24:33.8013471+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:24:48.801169+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:25:03.8014662+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:25:18.8013021+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:25:33.8011794+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:25:48.8010296+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:26:03.8014682+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:26:18.8010414+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:26:33.8012367+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:26:48.8012537+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:27:03.8014434+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:27:18.801395+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:27:33.8013777+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:27:48.801192+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:28:03.8010103+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:28:08.8941778+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": EOF"}
{"time":"2025-09-04T22:28:18.8013744+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:28:33.801137+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:28:48.7033008+08:00","level":"INFO","msg":"api: retrying HTTP error","status":502,"url":"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream","body":"\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>502 Server Error</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Server Error</h1>\n<h2>The server encountered a temporary error and could not complete your request.<p>Please try again in 30 seconds.</h2>\n<h2></h2>\n</body></html>\n"}
{"time":"2025-09-04T22:28:48.801431+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:29:03.8015108+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:29:18.8012481+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:29:33.8014476+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:29:39.2064836+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": EOF"}
{"time":"2025-09-04T22:29:48.801456+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:30:03.8012899+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:30:18.8011367+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:30:33.8013082+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:30:48.8013108+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:31:03.8013917+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:31:18.8014012+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:31:25.1410402+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": EOF"}
{"time":"2025-09-04T22:31:33.8011404+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:31:48.801291+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:32:03.8011718+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:32:18.8014354+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:32:33.8014159+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:32:48.8014082+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:33:03.8014623+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:33:18.801459+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:33:33.801202+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:33:48.8011127+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:34:03.8013215+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:34:18.801417+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:34:33.8015351+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:34:48.8010167+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:35:03.8014792+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:35:18.8011257+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:35:33.8012359+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:35:48.8010151+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:36:03.801189+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:36:18.8013998+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:36:33.8015085+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:36:48.8017398+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:37:03.8014797+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:37:18.8013628+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:37:33.801108+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:37:48.8014677+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:38:03.8011598+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:38:18.8014653+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:38:33.8012178+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:38:48.8011782+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:39:03.8012979+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:39:18.8014278+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:39:33.8015373+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:39:48.8012471+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:40:03.8010299+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:40:18.8010178+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:40:33.8012416+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:40:48.8011547+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:41:03.8011384+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:41:18.8010406+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:41:33.801412+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:41:48.8012005+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:42:03.8014723+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:42:18.8011056+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:42:33.8011762+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:42:48.8013216+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:43:03.80159+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:43:18.8015242+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:43:33.8014909+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:43:48.8012175+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:44:03.8012606+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:44:18.8010857+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:44:33.8014052+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:44:48.8012021+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:45:03.8012995+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:45:18.801392+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:45:33.8014296+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:45:48.8010313+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:46:03.8013874+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:46:18.8015695+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:46:33.801199+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:46:48.8014822+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:47:03.8011756+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:47:18.801104+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:47:23.7541598+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": EOF"}
{"time":"2025-09-04T22:47:33.8012036+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:47:48.8014258+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:48:03.8014893+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:48:09.7633921+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": EOF"}
{"time":"2025-09-04T22:48:18.8010707+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:48:33.8013144+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:48:48.8010763+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:49:03.8014427+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:49:18.801355+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:49:33.8014408+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:49:48.8014183+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:50:03.80114+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:50:18.8013623+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:50:33.8010796+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:50:48.8014042+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:51:03.801512+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:51:18.8012546+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:51:33.8012631+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:51:48.8011988+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:52:03.8013523+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:52:18.8010994+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:52:33.8014222+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:52:48.8012565+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:53:03.8014086+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:53:18.8011855+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:53:33.8010956+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:53:48.8009921+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:54:03.8010281+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:54:18.8013051+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:54:33.8012543+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:54:48.8014638+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:55:03.8012052+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:55:18.801419+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:55:33.801366+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:55:48.8014503+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:56:03.8013053+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:56:18.801137+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:56:33.8013969+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:56:48.8012936+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:57:03.8011051+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:57:18.8012383+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:57:33.8014843+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:57:48.8014558+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:58:03.8013285+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:58:18.8010752+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:58:33.8014107+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:58:48.8014269+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:59:03.8014316+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:59:18.8026491+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:59:33.8014432+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T22:59:48.8013994+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:00:03.8014208+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:00:18.8013998+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:00:33.8014745+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:00:48.801527+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:01:03.8011791+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:01:18.801492+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:01:33.8012114+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:01:48.8014756+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:02:03.801518+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:02:18.8013051+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:02:33.8012657+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:02:48.8014996+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:03:03.8011479+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:03:18.8010657+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:03:33.8012295+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:03:48.8014919+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:04:03.8010422+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:04:18.8014376+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:04:33.8013699+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:04:48.8010602+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:05:03.8010011+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:05:18.8010489+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:05:33.8011471+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:05:48.8011584+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:06:03.8012073+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:06:18.801138+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:06:33.8010811+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:06:48.8014715+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:07:03.8013186+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:07:18.801089+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:07:33.8011623+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:07:48.8011247+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:08:03.8014675+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:08:09.7403736+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": EOF"}
{"time":"2025-09-04T23:08:18.8014271+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:08:33.801122+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:08:48.8012036+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:09:03.8011937+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:09:18.8014369+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:09:33.8014709+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:09:48.801059+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:10:03.8011775+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:10:18.8012269+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:10:33.8011503+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:10:48.8014516+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:11:03.8011055+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:11:18.8015683+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:11:33.8013178+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:11:48.8011138+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:12:03.801416+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:12:09.7983488+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": EOF"}
{"time":"2025-09-04T23:12:18.8011965+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:12:33.8014712+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:12:48.8014324+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:13:03.8011306+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:13:18.8011345+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:13:33.8014474+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:13:48.8014856+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:13:54.8027313+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": EOF"}
{"time":"2025-09-04T23:14:03.8015542+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:14:18.8014868+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:14:33.8015083+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:14:48.801387+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:15:03.8010564+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:15:18.8015091+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:15:26.7606296+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": EOF"}
{"time":"2025-09-04T23:15:33.8022961+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:15:48.8013949+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:16:03.801136+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:16:18.8014661+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:16:33.8010751+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:16:48.8012408+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:17:03.8014241+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:17:18.8012855+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:17:33.8014316+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:17:48.8016162+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:18:03.8010868+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:18:10.8793622+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": EOF"}
{"time":"2025-09-04T23:18:18.8012757+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:18:33.8010707+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:18:46.2593775+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": unexpected EOF"}
{"time":"2025-09-04T23:18:48.8012249+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:19:03.8010136+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:19:18.8011051+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:19:33.8010711+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:19:48.801135+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:20:03.8013488+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:20:18.8012676+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:20:33.8012799+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:20:38.1682184+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": EOF"}
{"time":"2025-09-04T23:20:48.8011957+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:21:03.8014708+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:21:18.8011019+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:21:33.8013861+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:21:48.8012231+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:22:03.8011831+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:22:18.8014746+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:22:33.8014245+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:22:48.8011199+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:23:03.8011266+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:23:18.8013691+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:23:21.9264266+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": unexpected EOF"}
{"time":"2025-09-04T23:23:33.8013156+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:23:48.8014516+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:24:03.8011886+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:24:18.8011338+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:24:33.8012175+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:24:48.8012303+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:25:03.8013336+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:25:11.1035617+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": unexpected EOF"}
{"time":"2025-09-04T23:25:18.801256+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:25:33.8016246+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:25:48.8010595+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:26:03.801026+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:26:18.801311+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:26:33.8013603+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:26:48.8014987+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:27:03.801029+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:27:18.8010359+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:27:33.801396+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:27:48.8013408+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:28:03.8013893+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:28:18.8010916+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:28:33.8012549+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:28:48.8012662+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:29:03.8010821+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:29:18.8011577+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:29:33.8012473+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:29:48.8016111+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:30:03.8013831+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:30:18.8014085+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:30:33.8013788+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:30:48.8014707+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:31:03.8013765+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:31:06.0201623+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": unexpected EOF"}
{"time":"2025-09-04T23:31:18.8011157+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:31:33.8011381+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:31:48.8014195+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:32:03.801255+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:32:18.5977041+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": EOF"}
{"time":"2025-09-04T23:32:18.8012494+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:32:33.8013467+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:32:48.8012936+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:33:03.8011501+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:33:18.8015274+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:33:33.8013459+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:33:48.8013022+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:34:03.8012975+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:34:18.801232+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:34:33.8013946+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:34:48.8013947+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:35:03.8010954+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:35:18.8015019+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:35:33.8013211+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:35:48.8012883+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:36:03.8011882+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:36:18.8013606+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:36:33.8013081+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:36:48.8011962+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:37:03.8013278+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:37:18.8013939+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:37:19.0796402+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": unexpected EOF"}
{"time":"2025-09-04T23:37:33.8014356+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:37:48.8011393+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:38:03.8010903+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:38:18.8012236+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:38:33.8010202+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:38:48.8014697+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:39:03.8014805+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:39:18.8010434+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:39:33.8011545+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:39:48.8013971+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:40:03.8011756+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:40:18.8013266+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:40:33.8012234+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:40:39.2055474+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": EOF"}
{"time":"2025-09-04T23:40:48.8013819+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:41:03.8014786+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:41:18.8014186+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:41:33.8010826+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:41:48.8014057+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:42:03.8011608+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:42:18.8014364+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:42:33.8014638+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:42:48.80105+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:43:03.8013514+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:43:18.8012868+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:43:33.8013009+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:43:48.8014292+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:44:03.8013707+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:44:18.8011944+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:44:33.8012441+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:44:48.8014441+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:45:03.8014078+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:45:18.8012062+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:45:33.8013662+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:45:48.8013669+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:46:03.8011664+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:46:18.8014397+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:46:33.8014772+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:46:48.8015313+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:47:03.8013291+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:47:18.8012298+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:47:33.8010108+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:47:48.8014701+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:48:03.801452+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:48:18.8014705+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:48:33.8011265+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:48:48.8014817+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:49:03.8011811+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:49:18.8011038+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:49:33.8011766+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:49:48.8014451+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:50:03.8014158+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:50:18.8013484+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:50:33.801575+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:50:48.8013764+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:51:03.8010978+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:51:18.8014536+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:51:33.8012764+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:51:48.8010989+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:52:03.8014396+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:52:11.5204789+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": EOF"}
{"time":"2025-09-04T23:52:18.8012828+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:52:33.801159+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:52:35.0630448+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": unexpected EOF"}
{"time":"2025-09-04T23:52:48.8013349+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:53:03.8013869+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:53:18.8013587+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:53:33.8012709+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:53:48.8014115+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:54:03.801347+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:54:18.8012906+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:54:33.8012494+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:54:48.8014898+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:55:03.8014246+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:55:18.8013288+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:55:33.8011365+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:55:48.8011283+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:56:03.8012267+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:56:18.8013648+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:56:33.8014197+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:56:48.8013712+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:56:50.7903204+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": unexpected EOF"}
{"time":"2025-09-04T23:57:03.801294+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:57:06.6366132+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": unexpected EOF"}
{"time":"2025-09-04T23:57:18.8010128+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:57:33.8013629+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:57:48.8015377+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:58:03.8012672+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:58:18.8012661+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:58:33.8015062+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:58:48.8011614+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:59:03.8012028+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:59:18.8014582+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:59:33.8014219+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-04T23:59:48.8011878+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:00:03.8014652+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:00:18.8014682+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:00:33.8010753+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:00:48.8015437+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:01:03.8014012+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:01:18.8013142+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:01:33.8014611+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:01:48.801255+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:02:03.8014128+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:02:18.8012924+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:02:33.8012999+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:02:48.8014171+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:03:03.8012655+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:03:18.8015391+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:03:33.8012596+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:03:48.8015383+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:04:03.8014172+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:04:18.8016142+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:04:33.8014553+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:04:48.8017204+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:05:03.801412+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:05:18.8013977+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:05:33.8012032+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:05:48.8012554+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:06:03.8010425+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:06:18.8015226+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:06:33.8015149+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:06:48.2426573+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream\": unexpected EOF"}
{"time":"2025-09-05T00:06:48.8014517+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:07:03.8014003+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:07:18.8012136+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:07:33.8013182+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:07:48.8014218+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:08:03.801149+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:08:18.8014848+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:08:33.8011889+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:08:48.8013135+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:09:03.8013058+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:09:18.8013744+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:09:23.4822568+08:00","level":"ERROR","msg":"HTTP error","status":404,"method":"POST","url":"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream"}
{"time":"2025-09-05T00:09:23.4822568+08:00","level":"ERROR+4","msg":"filestream: fatal error: filestream: failed to upload: 404 Not Found path=files/xzk8559/recLSTM-bridge/lsfyno8h/file_stream: {\"error\":\"run recLSTM-bridge/lsfyno8h not found while streaming file\"}"}
{"time":"2025-09-05T00:09:33.8014658+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:09:48.8016314+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:10:03.8013134+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:10:18.8013053+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:10:33.8016243+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:10:48.8010344+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:11:03.8011162+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:11:18.8012204+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:11:33.801548+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:11:48.8011225+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:12:03.8011564+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:12:18.8013628+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:12:33.8010895+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:12:48.8011535+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:13:03.8012163+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:13:18.80148+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:13:33.8014415+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:13:48.8011038+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:14:03.8014184+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:14:18.8011673+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:14:33.801147+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-05T00:14:48.801477+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
