Shape of input  per step: torch.Size([120, 5])
Shape of output per step: torch.Size([120, 2])
Dataset length: 417330
Shape of input  per step: torch.Size([120, 5])
Shape of output per step: torch.Size([120, 2])
Dataset length: 395199
TrainingLSTM(
  (head): GRU(4, 256, batch_first=True)
  (hidden): ModuleList(
    (0-2): 3 x GRU(256, 256, batch_first=True)
  )
  (fc): Sequential(
    (0): Linear(in_features=256, out_features=256, bias=True)
    (1): Tanh()
    (2): Linear(in_features=256, out_features=2, bias=True)
  )
)
1451778 paramerters in total
0	0.09785538	0.09729732	0.00100	4.08
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.48 seconds
Model Performance:
- Correlation: 0.18403
- Root Mean Squared Error: 1.63 mm
- Peak Error: 99.727 %
D:\anaconda3\envs\torch22\lib\site-packages\torch\optim\lr_scheduler.py:143: UserWarning: Detected call of `lr_scheduler.step()` before `optimizer.step()`. In PyTorch 1.1.0 and later, you should call them in the opposite order: `optimizer.step()` before `lr_scheduler.step()`.  Failure to do this will result in PyTorch skipping the first value of the learning rate schedule. See more details at https://pytorch.org/docs/stable/optim.html#how-to-adjust-learning-rate
  warnings.warn("Detected call of `lr_scheduler.step()` before `optimizer.step()`. "
1	0.00597334	0.00678706	0.00100	30.96
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.20 seconds
Model Performance:
- Correlation: 0.39501
- Root Mean Squared Error: 1.62 mm
- Peak Error: 96.190 %
2	0.00739239	0.00568853	0.00099	60.11
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.93 seconds
Model Performance:
- Correlation: 0.66271
- Root Mean Squared Error: 1.49 mm
- Peak Error: 75.718 %
3	0.00554069	0.00480779	0.00099	89.06
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.28 seconds
Model Performance:
- Correlation: 0.00670
- Root Mean Squared Error: 52.96 mm
- Peak Error: 1615.443 %
4	0.00620601	0.00542841	0.00098	118.32
5	0.00529000	0.00457745	0.00098	129.51
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.05 seconds
Model Performance:
- Correlation: 0.05517
- Root Mean Squared Error: 20.73 mm
- Peak Error: 835.795 %
6	0.00405558	0.00443393	0.00097	159.05
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.09 seconds
Model Performance:
- Correlation: 0.73428
- Root Mean Squared Error: 1.38 mm
- Peak Error: 61.543 %
7	0.00583745	0.00469579	0.00097	187.68
8	0.00590515	0.00450867	0.00096	197.40
9	0.00478355	0.00415132	0.00096	207.26
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.38 seconds
Model Performance:
- Correlation: 0.15551
- Root Mean Squared Error: 8.68 mm
- Peak Error: 369.303 %
10	0.00406023	0.00444590	0.00095	234.95
11	0.00400550	0.00431886	0.00095	244.80
12	0.00508855	0.00450916	0.00094	254.72
13	0.00290081	0.00461162	0.00094	264.59
14	0.00443154	0.00451527	0.00093	274.46
15	0.00607743	0.00415800	0.00093	284.30
16	0.00672611	0.00424549	0.00093	294.13
17	0.00537771	0.00457145	0.00092	304.01
18	0.00363189	0.00424775	0.00092	313.72
19	0.00391716	0.00440119	0.00091	323.47
20	0.00750542	0.00458637	0.00091	333.49
21	0.00366238	0.00435009	0.00090	343.29
22	0.00652636	0.00432117	0.00090	353.12
23	0.00529017	0.00431453	0.00090	363.12
24	0.00377634	0.00451390	0.00089	373.34
25	0.00486899	0.00407034	0.00089	383.23
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.67 seconds
Model Performance:
- Correlation: 0.73669
- Root Mean Squared Error: 1.44 mm
- Peak Error: 53.472 %
26	0.00350288	0.00402238	0.00088	411.29
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.38 seconds
Model Performance:
- Correlation: 0.08347
- Root Mean Squared Error: 134.43 mm
- Peak Error: 9184.239 %
27	0.00414606	0.00395553	0.00088	440.51
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.81 seconds
Model Performance:
- Correlation: 0.03288
- Root Mean Squared Error: 663.43 mm
- Peak Error: 32908.498 %
28	0.00376741	0.00399169	0.00088	468.59
