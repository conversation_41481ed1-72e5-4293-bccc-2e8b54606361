import os
import numpy as np
import scipy.io
from scipy import signal
from utils.TimeHistoryDataset import TimeHistoryDataset, split_tra_val_dataset


def load_histories(args, train=True):
    idx = args.bridge_idx
    res = args.response
    fl_tra_acc = [
        'acc_tra_50Hz_0.15g.npy',
        'acc_tra_50Hz_0.3g.npy'
    ]
    fl_tra_dis = [
        f'dis_{res}/dis_{res}_bridge_{idx}_tra_50Hz_0.15g.npy',
        f'dis_{res}/dis_{res}_bridge_{idx}_tra_50Hz_0.3g.npy',
    ]
    fl_tes_acc = ['acc_tes_50Hz_0.15g.npy']
    fl_tes_dis = [f'dis_{res}/dis_{res}_bridge_{idx}_tes_50Hz_0.15g.npy']
    
    if train:
        fl_acc, fl_dis = fl_tra_acc, fl_tra_dis
    else:
        fl_acc, fl_dis = fl_tes_acc, fl_tes_dis
    
    '''
    对于三跨简支结构, 将沿纵向分布的6个代表性支座分别记为支座0-5, 
    其中支座0/5为桥台盖梁处支座, 支座1-4为中间盖梁处支座;
    统计表明, 桥梁支座的大变形/破坏位置几乎总是出现于中间盖梁而非桥台盖梁处, 
    且支座1/4的平均位移远高于支座2/3的平均位移, 因此仅考虑模拟支座1/4对应的位移响应;
    对于单跨简支结构, 仍模拟桥台盖梁处2个支座的位移响应.
    这是因为受桥台台后填土及结构形式的影响, 桥台的纵向刚度相对桥墩更大, 
    二者在相同的地震动激励下产生较大的相对位移, 
    由此反映为较大的边跨支座位移.
    Codes:
        d0 = np.load('data/dis_bear/dis_bear_bridge_14_tra_50Hz_0.15g.npy')
        d1 = np.load('data/dis_bear/dis_bear_bridge_14_tes_50Hz_0.15g.npy')
        plt.figure(figsize=(8,2), dpi=300)
        plt.scatter(np.arange(d0.shape[0]), np.argmax(np.abs(d0).max(1), 1), s=np.max(np.abs(d0).max(1), 1)*2000)
        plt.show()
        plt.figure(figsize=(8,2), dpi=300)
        plt.scatter(np.arange(d1.shape[0]), np.argmax(np.abs(d1).max(1), 1), s=np.max(np.abs(d1).max(1), 1)*2000)
        plt.show()
    '''
    histories = []
    for facc, fdis in zip(fl_acc, fl_dis):
        a = np.load(args.load_path + facc)  # (n_eq, n_step)
        d = np.load(args.load_path + fdis)  # (n_eq, n_step, n_dim)
        
        if res == 'bear' and d.shape[-1] == 6:
            d = d[..., [1, 4]]  # (n_eq, n_step, 2)
        
        # n_dim = 2 for all cases and responses
        h = np.concatenate((a[..., None], d), axis=-1)
        h = np.swapaxes(h, 1, 2)  # (n_eq, n_dim, n_step)
        histories += list(h)
    
    ls = args.local_sample_step
    if not train:
        scaler = Scaler(ls, args.save_path + '/scaler.npy')
    else:
        if args.resume != 'none':
            scaler = Scaler(ls, args.resume + '/scaler.npy')
        else:
            scaler = Scaler(ls, None)
            scaler.fit(histories)
        np.save(args.save_path + '/scaler.npy', scaler.max_abs_)
    
    return histories, scaler


def get_dataset(args, train=True):
    histories, scaler = load_histories(args, train=train)
    for i, f in enumerate(histories):
        if args.zero_init:
            f[1:] = f[1:] - f[1:, :1]
        if args.shift_acc:
            f[0, :-1] = f[0, 1:]
            f = f[:, :-1]
        histories[i] = scaler.scale(f).T.astype(np.float32)
    full_dataset = TimeHistoryDataset(histories, args, train=train)
    
    # if not train:
    #     return full_dataset, None
    
    tra, val = split_tra_val_dataset(full_dataset, train_ratio=args.split)
    print('Shape of input  per step: {}'.format(full_dataset[0][0].shape))
    print('Shape of output per step: {}'.format(full_dataset[0][1].shape))
    print('Dataset length: {}'.format(len(full_dataset)))
    return tra, val


def lowpass(data, f, fs):
    temp = np.concatenate((
        np.ones((500,)) * data[0],
        data,
        np.ones((500,)) * data[-1],
    ), 0)
    
    wn = 2*f/fs
    b, a = signal.butter(4, wn, 'lowpass')
    temp = signal.filtfilt(b, a, temp)
    return temp[500:-500]


class Scaler():
    def __init__(self, ls, path=None):
        self.ls = ls
        self.max_abs_ = None
        if path is not None:
            self.max_abs_ = np.load(path)
        
    def fit(self, flist):
        # flist.shape =  N * [C, T]
        self.max_abs_ = np.zeros((flist[0].shape[0]))
        for f in flist:
            d_increment = f[1:, self.ls:] - f[1:, :-self.ls]
            fnew = np.concatenate([f[:1, self.ls:], d_increment])
            temp = np.max(np.abs(fnew), axis=1)
            self.max_abs_ = np.maximum(self.max_abs_, temp)
        return
    
    def scale(self, f):
        if len(f.shape)==2:
            return f / self.max_abs_[:, np.newaxis]
        return f / self.max_abs_[np.newaxis, :, np.newaxis]
    

def cal_slice_params(args):
    idx = args.bridge_idx
    res = args.response
    d0 = np.load(args.load_path + f'dis_{res}/dis_{res}_bridge_{idx}_tra_50Hz_0.15g.npy')
    
    if res == 'bear' and d0.shape[-1] == 6:
        d0 = d0[..., [1, 4]]  # (n_eq, n_step, 2)
        
    d0 = np.swapaxes(d0, 1, 2)
    d0 = d0.reshape(-1, d0.shape[-1])
    
    n = int(1 / args.dt / 12.5) # to 12.5 Hz
    s = np.abs(np.fft.fft(d0))
    s = s[:, :s.shape[-1]//n]
    s[:, :50] = 0
    
    fq_max = np.argmax(s, axis=1).mean()
    t_main = 1 / (fq_max / s.shape[1] * 12.5)
    
    # cal params
    range_ph_ls = [int(3*t_main/args.dt), int(5*t_main/args.dt)]
    
    # make past_history*local_sample_step in range_ph_ls
    args.past_history = 100
    args.local_sample_step = int(np.clip(range_ph_ls[1]//args.past_history, 2, 5))
    args.past_history = int(np.clip(range_ph_ls[1] // args.local_sample_step, 50, 150))
    args.local_sample_step = int(range_ph_ls[1]//args.past_history)
    args.loss_length = int(np.minimum(args.loss_length, args.past_history*0.3))

    print('Automatically calculated slice parameters:')
    print('past history     : {}'.format(args.past_history))
    print('local sample step: {}'.format(args.local_sample_step))
    print('receptive field  : {}'.format(range_ph_ls))
    return args