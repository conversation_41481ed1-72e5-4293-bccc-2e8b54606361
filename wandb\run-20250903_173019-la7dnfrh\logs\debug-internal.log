{"time":"2025-09-03T17:30:20.0047008+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-09-03T17:30:22.8481834+08:00","level":"INFO","msg":"stream: created new stream","id":"la7dnfrh"}
{"time":"2025-09-03T17:30:22.8481834+08:00","level":"INFO","msg":"stream: started","id":"la7dnfrh"}
{"time":"2025-09-03T17:30:22.8481834+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"la7dnfrh"}
{"time":"2025-09-03T17:30:22.8481834+08:00","level":"INFO","msg":"sender: started","stream_id":"la7dnfrh"}
{"time":"2025-09-03T17:30:22.8481834+08:00","level":"INFO","msg":"handler: started","stream_id":"la7dnfrh"}
{"time":"2025-09-03T17:30:24.1464847+08:00","level":"ERROR","msg":"git repo not found","error":"repository does not exist"}
{"time":"2025-09-03T18:53:44.6034686+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/la7dnfrh/file_stream\": unexpected EOF"}
{"time":"2025-09-03T19:40:00.7802649+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": unexpected EOF"}
{"time":"2025-09-03T20:09:30.8528796+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/la7dnfrh/file_stream\": EOF"}
{"time":"2025-09-03T20:42:16.7636159+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/la7dnfrh/file_stream\": unexpected EOF"}
{"time":"2025-09-03T20:51:59.5633243+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/la7dnfrh/file_stream\": EOF"}
{"time":"2025-09-03T21:07:54.8264289+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/la7dnfrh/file_stream\": unexpected EOF"}
{"time":"2025-09-03T22:20:59.5167816+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/la7dnfrh/file_stream\": EOF"}
{"time":"2025-09-03T22:25:50.5408434+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-09-03T22:26:11.40277+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": unexpected EOF"}
{"time":"2025-09-03T22:26:46.2220752+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded"}
{"time":"2025-09-03T22:26:56.2668351+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/la7dnfrh/file_stream\": EOF"}
{"time":"2025-09-03T22:27:25.7391126+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled while waiting for connection (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-09-03T22:28:12.3963593+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled while waiting for connection (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-09-03T22:28:37.1516673+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/la7dnfrh/file_stream\": unexpected EOF"}
{"time":"2025-09-03T23:16:20.8635001+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/la7dnfrh/file_stream\": EOF"}
{"time":"2025-09-03T23:19:22.5024457+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/la7dnfrh/file_stream\": EOF"}
{"time":"2025-09-03T23:21:10.375896+08:00","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"uploading wandb-summary.json","runtime_seconds":0.5710503},{"desc":"uploading output.log","runtime_seconds":0.5710503},{"desc":"uploading config.yaml","runtime_seconds":0.0509571}],"total_operations":3}}
{"time":"2025-09-03T23:21:13.739342+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-09-03T23:21:14.9154794+08:00","level":"INFO","msg":"stream: closing","id":"la7dnfrh"}
{"time":"2025-09-03T23:21:14.9154794+08:00","level":"INFO","msg":"handler: closed","stream_id":"la7dnfrh"}
{"time":"2025-09-03T23:21:14.9154794+08:00","level":"INFO","msg":"sender: closed","stream_id":"la7dnfrh"}
{"time":"2025-09-03T23:21:14.9154794+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"la7dnfrh"}
{"time":"2025-09-03T23:21:14.9154794+08:00","level":"INFO","msg":"stream: closed","id":"la7dnfrh"}
