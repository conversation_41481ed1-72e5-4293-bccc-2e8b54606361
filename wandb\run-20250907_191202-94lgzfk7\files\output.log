Traceback (most recent call last):
  File "E:\Codes\recLSTM-bridge\train.py", line 24, in <module>
    dtra, dval = get_dataset(args)
  File "E:\Codes\recLSTM-bridge\utils_data.py", line 85, in get_dataset
    print('Shape of input  per step: {}'.format(full_dataset[0][0].shape))
  File "E:\Codes\recLSTM-bridge\utils\TimeHistoryDataset.py", line 30, in __getitem__
    if self.train and (not self.sym) and np.random.rand() > 0.5:
AttributeError: 'TimeHistoryDataset' object has no attribute 'sym'
