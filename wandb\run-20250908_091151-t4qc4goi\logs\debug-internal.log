{"time":"2025-09-08T09:11:52.2611364+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-09-08T09:11:53.9557368+08:00","level":"INFO","msg":"stream: created new stream","id":"t4qc4goi"}
{"time":"2025-09-08T09:11:53.9557368+08:00","level":"INFO","msg":"stream: started","id":"t4qc4goi"}
{"time":"2025-09-08T09:11:53.9557368+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"t4qc4goi"}
{"time":"2025-09-08T09:11:53.9557368+08:00","level":"INFO","msg":"sender: started","stream_id":"t4qc4goi"}
{"time":"2025-09-08T09:11:53.9557368+08:00","level":"INFO","msg":"handler: started","stream_id":"t4qc4goi"}
{"time":"2025-09-08T09:11:54.638582+08:00","level":"ERROR","msg":"git repo not found","error":"repository does not exist"}
{"time":"2025-09-08T09:40:44.6781779+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T10:34:12.663853+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T10:34:34.4563777+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T10:35:25.4295115+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T10:35:41.3624097+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T10:36:06.9198303+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T10:36:56.0094306+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T10:37:27.3174136+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T10:37:59.0492923+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T10:47:52.3940482+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T10:51:38.5747023+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T11:01:59.0708817+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T11:26:59.0672076+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T11:34:28.9861837+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T11:40:54.701653+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T11:41:11.0372634+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T11:48:01.0846352+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T12:13:59.4681231+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T12:14:35.7873848+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T12:16:29.1595619+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T12:16:47.7073568+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T12:17:59.6857209+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T12:21:39.2279962+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T12:22:06.3243757+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T12:22:16.1048339+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T12:23:44.5197636+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T12:25:49.7155869+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T12:26:53.6763373+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T12:27:51.311978+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T12:30:52.4180863+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T12:31:17.7480943+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T12:31:40.7504937+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T12:34:00.3827556+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T12:34:16.3581318+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T12:35:47.3902522+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T12:36:02.2700041+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T12:42:47.3313664+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T12:45:14.1833159+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T12:46:25.879174+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T12:48:59.3095586+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T12:49:48.5084694+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T12:50:22.2466943+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T12:52:37.5382909+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T12:53:22.3466325+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T12:55:20.6971277+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T12:56:12.2761071+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T12:56:26.402436+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T12:57:34.6560004+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T12:59:00.1228161+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T13:00:16.1518762+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T13:00:59.914421+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T13:01:52.1363195+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T13:06:44.9962547+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T13:07:14.0314163+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T13:11:37.2019827+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T13:11:51.9532117+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T13:12:14.7322607+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T13:14:23.9861882+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T13:14:37.9228971+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T13:26:29.41483+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T13:27:23.5011317+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T13:32:58.9786412+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T13:33:43.9862361+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T13:40:13.9684181+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T14:02:45.0263981+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T14:02:59.0106522+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T14:28:45.1344942+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T14:32:11.0912566+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T14:34:35.8057915+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T14:34:55.611608+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T14:36:08.0449215+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T14:47:30.2338344+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T14:48:30.0568854+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T14:49:00.8350348+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T15:13:43.9674878+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T15:26:00.0949407+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T15:36:45.0269136+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T15:53:06.3028989+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T15:53:29.7043547+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T16:04:44.2383204+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T16:13:47.6585021+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T16:16:09.4690663+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T16:16:59.2664228+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T16:19:14.633619+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T16:38:08.9393899+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T16:54:17.2947513+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T17:12:32.5181148+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T17:12:59.8099901+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T17:23:30.1629311+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T17:41:21.3005875+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T18:03:56.3158116+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T18:06:29.2145781+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T18:30:44.2097698+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T18:49:47.4852108+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T18:50:29.8697632+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T18:51:01.5840392+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T19:07:28.9980385+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T19:10:51.026856+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T19:15:29.4380584+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T19:22:47.1542114+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T19:30:28.9892039+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T19:45:59.4699867+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T20:16:59.1759599+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T20:29:36.4185389+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T20:31:48.3863427+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T20:56:14.2684581+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T20:57:02.0275824+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T21:09:04.7244898+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T21:09:32.8649186+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T21:10:56.2801685+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T21:13:01.8961598+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T21:16:16.7966992+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T21:33:07.446295+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T21:40:45.7752253+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T21:48:07.7849396+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T21:48:31.7712717+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T21:58:31.3976997+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T22:08:05.8726064+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T22:09:08.04533+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T22:22:26.491848+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T22:38:30.7713802+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T22:43:31.2366641+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T22:43:47.7341579+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T22:47:22.7822815+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T22:48:15.7757793+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T23:05:01.9363087+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T23:16:19.3023916+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": unexpected EOF"}
{"time":"2025-09-08T23:19:00.9229538+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-08T23:28:16.1674124+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-09T00:00:15.7630928+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-09T09:00:31.125285+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-09T09:05:30.7648042+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-09T10:28:31.9603158+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-09T10:30:30.794117+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-09T10:54:47.2065967+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
{"time":"2025-09-09T11:26:46.7790073+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/t4qc4goi/file_stream\": EOF"}
