2025-09-03 00:29:45,193 INFO    MainThread:13948 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-09-03 00:29:45,193 INFO    MainThread:13948 [wandb_setup.py:_flush():80] Configure stats pid to 13948
2025-09-03 00:29:45,193 INFO    MainThread:13948 [wandb_setup.py:_flush():80] Loading settings from C:\Users\<USER>\.config\wandb\settings
2025-09-03 00:29:45,193 INFO    MainThread:13948 [wandb_setup.py:_flush():80] Loading settings from E:\Codes\recLSTM-bridge\wandb\settings
2025-09-03 00:29:45,193 INFO    MainThread:13948 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-09-03 00:29:45,193 INFO    MainThread:13948 [wandb_init.py:setup_run_log_directory():703] Logging user logs to E:\Codes\recLSTM-bridge\wandb\run-20250903_002945-ec2525g0\logs\debug.log
2025-09-03 00:29:45,202 INFO    MainThread:13948 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to E:\Codes\recLSTM-bridge\wandb\run-20250903_002945-ec2525g0\logs\debug-internal.log
2025-09-03 00:29:45,202 INFO    MainThread:13948 [wandb_init.py:init():830] calling init triggers
2025-09-03 00:29:45,202 INFO    MainThread:13948 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'bridge_idx': 13, 'response': 'bear', 'load_path': 'data/', 'save_path': 'save/', 'pred_dim': 1, 'dt': 0.02, 'zero_init': False, 'shift_acc': False, 'flip': False, 'fliplr': True, 'auto_slice': True, 'past_history': 64, 'local_sample_step': 2, 'global_sample_step': 1, 'window_sliding_step': 1, 'layers': 4, 'width': 256, 'split': 0.95, 'epochs': 2000, 'initial_epoch': 0, 'batch_size': 512, 'initial_lr': 0.001, 'decay': 0.005, 'resume': 'none', 'loss_length': 19, 'use_amp': True, 'adan': False, 'max_grad_norm': 0.0, 'weight_decay': 0.0, 'opt_eps': 1e-08, 'opt_betas': None, 'no_prox': False, '_wandb': {'code_path': 'code/train.py'}}
2025-09-03 00:29:45,202 INFO    MainThread:13948 [wandb_init.py:init():871] starting backend
2025-09-03 00:29:45,530 INFO    MainThread:13948 [wandb_init.py:init():874] sending inform_init request
2025-09-03 00:29:45,547 INFO    MainThread:13948 [wandb_init.py:init():882] backend started and connected
2025-09-03 00:29:45,548 INFO    MainThread:13948 [wandb_init.py:init():953] updated telemetry
2025-09-03 00:29:45,549 INFO    MainThread:13948 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-09-03 00:29:47,267 INFO    MainThread:13948 [wandb_init.py:init():1029] starting run threads in backend
2025-09-03 00:29:47,347 INFO    MainThread:13948 [wandb_run.py:_console_start():2458] atexit reg
2025-09-03 00:29:47,347 INFO    MainThread:13948 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-09-03 00:29:47,347 INFO    MainThread:13948 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-09-03 00:29:47,347 INFO    MainThread:13948 [wandb_run.py:_redirect():2398] Redirects installed.
2025-09-03 00:29:47,348 INFO    MainThread:13948 [wandb_init.py:init():1075] run started, returning control to user process
2025-09-03 00:29:51,953 INFO    MsgRouterThr:13948 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 1 handles.
