{"time":"2025-09-03T13:50:00.4062244+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-09-03T13:50:02.9277026+08:00","level":"INFO","msg":"stream: created new stream","id":"m4fpclpk"}
{"time":"2025-09-03T13:50:02.9277026+08:00","level":"INFO","msg":"stream: started","id":"m4fpclpk"}
{"time":"2025-09-03T13:50:02.9282126+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"m4fpclpk"}
{"time":"2025-09-03T13:50:02.9282126+08:00","level":"INFO","msg":"handler: started","stream_id":"m4fpclpk"}
{"time":"2025-09-03T13:50:02.9282126+08:00","level":"INFO","msg":"sender: started","stream_id":"m4fpclpk"}
{"time":"2025-09-03T13:50:03.889846+08:00","level":"ERROR","msg":"git repo not found","error":"repository does not exist"}
{"time":"2025-09-03T14:16:38.7796517+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/m4fpclpk/file_stream\": EOF"}
{"time":"2025-09-03T14:20:08.9305068+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/m4fpclpk/file_stream\": EOF"}
{"time":"2025-09-03T14:25:24.4550161+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/m4fpclpk/file_stream\": EOF"}
{"time":"2025-09-03T15:07:41.9354632+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/m4fpclpk/file_stream\": EOF"}
{"time":"2025-09-03T15:09:10.853723+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/m4fpclpk/file_stream\": EOF"}
{"time":"2025-09-03T15:09:47.4413656+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/m4fpclpk/file_stream\": unexpected EOF"}
{"time":"2025-09-03T15:30:24.9220535+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/m4fpclpk/file_stream\": EOF"}
{"time":"2025-09-03T15:34:24.7680197+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/m4fpclpk/file_stream\": EOF"}
{"time":"2025-09-03T15:39:31.2792428+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/m4fpclpk/file_stream\": unexpected EOF"}
{"time":"2025-09-03T15:59:54.0305169+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/m4fpclpk/file_stream\": EOF"}
{"time":"2025-09-03T16:00:04.0970586+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/m4fpclpk/file_stream\": EOF"}
{"time":"2025-09-03T18:30:49.7738247+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/m4fpclpk/file_stream\": EOF"}
{"time":"2025-09-03T18:44:08.772435+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/m4fpclpk/file_stream\": EOF"}
