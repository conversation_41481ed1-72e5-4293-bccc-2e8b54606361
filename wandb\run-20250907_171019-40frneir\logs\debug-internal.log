{"time":"2025-09-07T17:10:19.6471114+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-09-07T17:10:21.0422449+08:00","level":"INFO","msg":"stream: created new stream","id":"40frneir"}
{"time":"2025-09-07T17:10:21.0422449+08:00","level":"INFO","msg":"stream: started","id":"40frneir"}
{"time":"2025-09-07T17:10:21.0422449+08:00","level":"INFO","msg":"handler: started","stream_id":"40frneir"}
{"time":"2025-09-07T17:10:21.0422449+08:00","level":"INFO","msg":"sender: started","stream_id":"40frneir"}
{"time":"2025-09-07T17:10:21.0422449+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"40frneir"}
{"time":"2025-09-07T17:10:21.7819934+08:00","level":"ERROR","msg":"git repo not found","error":"repository does not exist"}
{"time":"2025-09-07T17:26:26.5195811+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/40frneir/file_stream\": EOF"}
{"time":"2025-09-07T17:37:14.3463494+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/40frneir/file_stream\": EOF"}
{"time":"2025-09-07T17:37:51.4079515+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/40frneir/file_stream\": unexpected EOF"}
{"time":"2025-09-07T17:47:21.7808722+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:47:36.780649+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:47:51.7809826+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:48:06.7810356+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:48:21.7808245+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:48:36.7809597+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:48:51.7809341+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:49:06.7807438+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:49:21.7808223+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:49:36.7807353+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:49:51.7806328+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:50:06.7805533+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:50:21.7804877+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:50:36.7810242+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:50:51.7809916+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:51:06.780851+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:51:21.780784+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:51:36.7809422+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:51:51.7805049+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:52:06.7808393+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:52:21.7808581+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:52:36.780979+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:52:51.7810029+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:53:06.7806939+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:53:14.7595233+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/40frneir/file_stream\": EOF"}
{"time":"2025-09-07T17:53:21.7807942+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:53:36.7808936+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:53:51.7807862+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:54:06.7806171+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:54:21.780821+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:54:29.0214282+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/40frneir/file_stream\": EOF"}
{"time":"2025-09-07T17:54:36.7806731+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:54:51.7808016+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:55:06.7805191+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:55:21.7806513+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:55:36.7809005+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:55:51.7806821+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:56:06.7807088+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:56:21.7810017+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:56:36.7810209+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:56:51.7808041+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:57:06.7807421+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:57:21.7809159+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:57:36.780999+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:57:51.7808844+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:58:06.7808617+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:58:21.7807954+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:58:36.7806147+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:58:51.7807798+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:59:06.7805172+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:59:21.7810441+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:59:31.707636+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/40frneir/file_stream\": EOF"}
{"time":"2025-09-07T17:59:36.7809857+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T17:59:51.780588+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:00:06.7808412+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:00:21.7810719+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:00:36.7806794+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:00:51.7806284+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:01:06.7809564+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:01:21.7808872+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:01:36.7805292+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:01:51.7809791+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:02:06.7805758+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:02:21.7809324+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:02:36.7806998+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:02:51.7808742+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:03:06.7809346+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:03:21.7808096+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:03:36.7806034+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:03:51.780964+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:04:06.7808784+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:04:21.7807551+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:04:36.7808061+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:04:51.7807687+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:05:06.7807089+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:05:21.7809065+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:05:36.7806727+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:05:51.7809787+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:06:06.7809302+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:06:21.78077+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:06:36.7807082+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:06:51.7808277+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:07:06.7805803+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:07:21.7809264+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:07:36.781016+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:07:51.7809813+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:08:06.7805821+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:08:21.780625+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:08:36.7811614+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:08:51.7807423+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:09:06.7806453+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:09:21.7805168+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:09:36.7806272+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:09:51.7805281+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:10:06.780697+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:10:21.7805642+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:10:36.7805615+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:10:51.7806484+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:11:06.7805387+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:11:21.7808046+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:11:36.7809152+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:11:51.78103+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:11:59.9198413+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/40frneir/file_stream\": EOF"}
{"time":"2025-09-07T18:12:06.7807036+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:12:21.7808148+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:12:36.7809035+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:12:51.7808158+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:13:06.7807318+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:13:21.781035+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:13:36.7808328+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:13:51.7808254+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:14:06.7804978+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:14:21.7807067+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:14:36.7809974+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:14:51.7807541+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:15:06.7808516+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:15:21.7809231+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:15:29.75993+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/40frneir/file_stream\": EOF"}
{"time":"2025-09-07T18:15:36.7808096+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:15:51.7806124+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:16:06.7807658+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:16:21.7807258+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:16:36.780615+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:16:51.7806948+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:17:06.7809217+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:17:21.7805027+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:17:36.7808293+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:17:51.7808541+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:18:06.7807562+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:18:21.7808363+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:18:36.7807844+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:18:51.780496+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:19:06.7804972+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:19:21.7806838+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:19:36.7807602+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:19:51.7808343+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:20:06.7809763+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:20:21.7806139+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:20:36.7805243+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:20:51.7808157+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:21:06.7808941+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:21:21.7808568+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:21:36.7806315+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:21:51.7808317+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:22:06.7806886+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:22:21.7806948+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:22:36.780855+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:22:51.7808165+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:23:06.7808156+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:23:21.7807296+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:23:36.7806317+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:23:51.780878+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:24:06.7805318+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:24:21.7805785+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:24:36.7807925+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:24:51.7805447+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:25:06.7806485+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:25:21.78054+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:25:36.7808362+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:25:51.7808033+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:26:06.7805653+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:26:21.7809796+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:26:36.7806928+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:26:51.7808662+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:27:06.7806139+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:27:21.7808151+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:27:36.7805205+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:27:51.7806077+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:27:58.7505535+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/40frneir/file_stream\": EOF"}
{"time":"2025-09-07T18:28:06.7805778+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:28:21.7808597+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:28:36.7805088+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:28:51.7808755+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:29:06.7810645+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:29:21.780722+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:29:36.7809617+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:29:51.7810824+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:30:06.7809274+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:30:21.7807134+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:30:36.7810362+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:30:51.7809487+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:31:06.7807142+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:31:21.7805705+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:31:36.7807896+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:31:51.7807645+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:32:06.7809006+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:32:21.780645+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:32:28.7475524+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/40frneir/file_stream\": EOF"}
{"time":"2025-09-07T18:32:36.7805204+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:32:51.7807665+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:33:06.7805834+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:33:21.7805175+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:33:36.7806252+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:33:51.7808246+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:34:06.7805925+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:34:21.7806468+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:34:36.7806608+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:34:51.7809722+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:35:06.7809244+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:35:21.7808688+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:35:36.7809331+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:35:51.7805341+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:36:06.7807688+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:36:21.7805028+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:36:36.7809237+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:36:51.780849+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:37:06.7807854+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:37:21.7806178+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:37:36.7808729+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:37:51.780633+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:38:06.7808998+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:38:21.7805654+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:38:36.7805853+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:38:51.780984+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:39:06.7806934+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:39:21.780895+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:39:36.7808914+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:39:51.7808419+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:40:06.7805638+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:40:21.7809918+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:40:36.780983+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:40:51.7807236+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:41:06.7805918+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:41:21.7805777+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:41:36.7806187+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:41:51.7808273+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:42:06.7804849+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:42:21.7808974+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:42:36.7806404+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:42:51.7805991+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:43:06.7805084+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:43:21.7808649+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:43:36.7809545+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:43:51.7809502+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:44:06.7809161+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:44:21.7810488+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:44:36.7806981+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:44:51.7807647+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:45:06.7804948+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:45:21.7807957+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:45:36.7806569+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:45:51.7806367+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:46:06.7807948+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:46:21.7805142+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:46:36.7808979+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:46:51.7808283+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:47:06.7808731+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:47:21.780563+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:47:36.7807032+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:47:51.7806959+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:48:06.7809623+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:48:21.7805536+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:48:36.7806723+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:48:51.7805236+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:49:06.7805569+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:49:21.7805935+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:49:36.7807926+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:49:51.7808333+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:50:06.7808395+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:50:13.74146+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/40frneir/file_stream\": EOF"}
{"time":"2025-09-07T18:50:21.7805901+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:50:36.780951+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:50:51.7805327+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:51:06.7808835+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:51:21.7808471+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:51:36.7807507+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:51:51.781332+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:52:06.780868+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:52:21.7807726+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:52:36.7808791+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:52:51.780782+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:53:06.7806102+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:53:21.7806748+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:53:36.780765+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:53:51.7810051+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:54:06.7808628+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:54:21.7806712+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:54:36.7808142+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:54:51.7806561+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:55:06.7809137+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:55:21.7809012+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:55:36.7810761+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:55:51.7807995+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:56:06.7808876+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:56:21.7805701+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:56:36.7804865+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:56:51.7809084+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:57:06.7806238+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:57:21.7805133+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:57:36.7810379+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:57:51.780807+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:58:06.7805561+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:58:21.7805968+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:58:36.7808269+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:58:51.7807883+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:59:06.7807863+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:59:21.7804798+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:59:36.7807388+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T18:59:51.7808957+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:00:06.7809268+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:00:21.7806707+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:00:36.7810094+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:00:51.7805794+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:01:06.7809724+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:01:21.7809352+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:01:36.780633+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:01:51.7809958+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:02:06.7805374+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:02:21.7805153+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:02:36.7807451+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:02:51.7808239+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:03:06.7807751+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:03:21.7805464+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:03:36.7809256+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:03:51.7806848+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:04:06.7805644+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:04:21.7809294+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:04:36.7807094+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:04:51.7804969+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:05:06.7807968+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:05:21.7805883+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:05:28.902056+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/40frneir/file_stream\": EOF"}
{"time":"2025-09-07T19:05:36.7809058+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:05:51.7807865+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:06:06.7808694+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:06:21.7805107+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:06:36.7806849+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:06:51.7807141+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:07:06.7809719+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:07:21.7806796+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:07:36.7808311+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:07:51.7809995+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:08:06.7806194+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:08:21.7808508+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:08:36.7810044+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:08:51.7810157+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:09:06.7809095+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:09:21.7807192+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:09:36.7810068+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:09:51.7807301+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:10:06.7809339+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:10:21.7808345+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:10:36.7809138+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:10:51.7807886+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:11:06.7807478+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:11:21.780617+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:11:36.7805338+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:11:51.7808318+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:12:06.7812178+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:12:21.780634+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:12:36.7809867+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:12:51.7808537+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:13:06.7805656+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:13:21.7807364+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:13:36.7805488+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:13:51.7811194+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:14:06.7809691+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:14:21.780816+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:14:36.7809007+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:14:51.7810658+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:14:58.7444197+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/40frneir/file_stream\": EOF"}
{"time":"2025-09-07T19:15:06.780907+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:15:21.7807868+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:15:36.7805994+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:15:51.7809604+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:16:06.7809579+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:16:21.7808302+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:16:36.7809735+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:16:51.7806173+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:17:06.7806148+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:17:21.7804901+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:17:36.7805982+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:17:51.7809116+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:18:06.7809535+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:18:21.7808439+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:18:36.7806126+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:18:51.7805912+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:19:06.781057+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:19:21.7806375+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:19:36.7804811+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:19:51.7809667+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:20:06.7810174+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:20:21.7807785+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:20:36.7807383+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:20:51.7807956+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:21:06.7806822+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:21:21.780816+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:21:36.7806047+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:21:47.2470139+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/40frneir/file_stream\": EOF"}
{"time":"2025-09-07T19:21:51.7808687+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:22:06.7807825+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:22:21.7805333+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:22:36.7806536+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:22:51.7807967+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:23:06.7806144+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:23:21.7810145+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:23:36.7809603+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:23:51.7808909+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:24:06.781097+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:24:21.7809246+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:24:36.7809116+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:24:51.7805992+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:25:06.7806099+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:25:21.7807885+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:25:36.7807242+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:25:51.7807026+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:26:06.7809649+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:26:21.7809276+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:26:36.7809256+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:26:51.780737+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:27:06.78076+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:27:21.7810067+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:27:36.7808776+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:27:51.7809055+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:28:06.7809986+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:28:21.780838+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:28:36.7806882+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:28:51.7806112+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:29:06.7807968+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:29:21.780855+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:29:36.7805751+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:29:51.7809115+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:30:06.7808708+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:30:21.780776+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:30:36.7809767+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:30:51.7808193+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:31:06.7806239+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:31:08.5312782+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/40frneir/file_stream\": unexpected EOF"}
{"time":"2025-09-07T19:31:21.7809726+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:31:36.7805826+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:31:51.7809675+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:32:06.7808477+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:32:21.7808481+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:32:36.7808034+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:32:51.7807085+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:33:06.7808905+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:33:21.7809694+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:33:36.7805938+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:33:51.780981+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:34:06.7809759+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:34:21.7806491+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:34:36.780646+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:34:51.78086+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:35:06.7807711+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:35:21.7807388+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:35:36.7806205+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:35:51.7810345+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:36:06.7807884+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:36:21.7807205+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:36:36.7809208+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:36:51.780793+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:37:06.7806149+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:37:21.7810607+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:37:36.7807219+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:37:51.7809602+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:38:06.7805982+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:38:21.7808951+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:38:36.7806144+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:38:51.7807278+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:39:06.7809256+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:39:21.7807448+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:39:36.7807472+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:39:51.7807671+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:40:06.7808049+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:40:21.7808282+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:40:36.7806879+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:40:51.780739+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:41:06.7809071+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:41:21.7808886+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:41:36.780834+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:41:51.780843+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:42:06.7805983+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:42:21.781055+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:42:36.7807133+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:42:51.7805457+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:43:06.7808074+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:43:21.7805536+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:43:36.7805227+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:43:51.7806679+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:44:06.7809644+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:44:21.7808265+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:44:36.7806282+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:44:51.7806334+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:45:06.7806099+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:45:21.7805943+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:45:24.117331+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/40frneir/file_stream\": EOF"}
{"time":"2025-09-07T19:45:36.7805443+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:45:51.7807504+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:46:06.7807801+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:46:21.7811295+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:46:36.7806904+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:46:51.7806375+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:47:06.7807423+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:47:21.7805261+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:47:36.7807498+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:47:51.780799+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:48:06.7809051+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:48:21.7806669+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:48:36.7808911+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:48:51.7807125+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:49:06.7810309+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:49:21.7805265+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:49:36.7809453+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:49:44.8792195+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/40frneir/file_stream\": EOF"}
{"time":"2025-09-07T19:49:51.7806062+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:50:06.7328524+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/40frneir/file_stream\": EOF"}
{"time":"2025-09-07T19:50:06.7809279+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:50:21.7808752+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:50:36.780698+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:50:51.7806964+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:51:06.780948+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:51:21.7810499+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:51:36.7807946+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:51:51.7807127+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:52:06.7808349+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:52:21.7805196+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:52:36.7806618+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:52:51.7809043+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:53:06.7806254+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:53:21.7805943+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:53:36.7809208+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:53:51.780749+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:54:06.7808309+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:54:21.780816+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:54:36.7806569+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:54:51.7809967+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:55:06.7809937+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:55:21.7809381+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:55:36.7808244+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:55:51.7805361+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:56:06.7807856+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:56:21.7809632+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:56:36.7808315+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:56:51.7807535+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:57:06.7809597+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:57:21.7806065+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:57:36.7805769+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:57:51.7805429+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:58:06.7806981+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:58:21.7805666+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:58:36.7807787+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:58:51.7806911+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:59:06.7807876+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:59:21.7806538+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:59:36.781022+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:59:51.7809246+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:00:06.7807998+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:00:21.7809307+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:00:36.7809522+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:00:51.7808628+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:01:06.780519+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:01:21.7808282+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:01:36.7810265+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:01:51.7806014+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:02:06.7806946+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:02:21.780864+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:02:36.7807093+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:02:51.7810699+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:03:06.7806235+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:03:20.783045+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/40frneir/file_stream\": EOF"}
{"time":"2025-09-07T20:03:21.7808275+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:03:36.7807927+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:03:51.7807836+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:04:06.7809304+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:04:21.7805851+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:04:36.7808816+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:04:51.7809871+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:05:06.7810065+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:05:21.7809664+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:05:36.7807857+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:05:51.7806171+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:05:58.8006031+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/40frneir/file_stream\": EOF"}
{"time":"2025-09-07T20:06:06.7808911+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:06:21.7805983+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:06:36.7808224+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:06:51.7807567+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:07:06.780944+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:07:21.7804953+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:07:36.7810168+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:07:51.7807143+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:08:06.7807361+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:08:21.7805508+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:08:36.7805025+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:08:51.7809701+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:09:06.7809659+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:09:21.780573+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:09:36.7805216+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:09:51.780571+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:10:06.7811782+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:10:21.7813325+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:10:36.7809436+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:10:51.7805682+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:11:06.7808124+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:11:21.7805841+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:11:36.7806903+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:11:51.7807553+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:12:06.7809047+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:12:21.7805848+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:12:36.7808406+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:12:51.7804682+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:13:06.7807058+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:13:21.780964+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:13:36.7810115+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:13:51.7809745+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:14:06.7806508+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:14:21.7808474+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:14:36.7807343+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:14:51.7809954+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:15:06.7807282+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:15:21.780727+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:15:36.780598+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:15:51.7807678+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:16:06.7806106+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:16:21.7807122+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:16:36.7810785+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:16:51.7808637+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:17:06.7805951+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:17:21.78092+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:17:36.780494+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:17:51.7805966+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:18:06.7809172+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:18:21.7808616+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:18:36.7809955+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:18:51.7809051+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:19:06.7808935+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:19:21.7807001+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:19:36.7810944+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:19:51.7808693+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:20:06.7805488+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:20:21.7805444+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:20:36.7808073+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:20:51.78068+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:21:06.7804975+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:21:21.7806445+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:21:36.7809246+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:21:51.7808949+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:22:06.7806529+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:22:21.7808437+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:22:36.7806087+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:22:51.7809539+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:23:06.7809062+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:23:21.7807963+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:23:36.780873+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:23:51.7805761+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:24:06.7809159+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:24:21.7805876+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:24:36.7807916+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:24:51.7809639+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:25:06.7808688+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:25:21.7807101+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:25:36.7811501+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:25:51.7810104+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:26:06.7808572+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:26:21.7806375+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:26:36.7810777+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:26:51.7807584+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:27:06.78092+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:27:21.7806052+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:27:36.7805891+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:27:51.7806988+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:28:06.7809664+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:28:21.7810764+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:28:36.7806535+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:28:51.7805649+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:29:06.7809369+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:29:21.7808897+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:29:36.7811942+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:29:51.7805733+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:30:06.7809944+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:30:21.7806359+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:30:36.7805755+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:30:51.7809373+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:31:06.7804966+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:31:21.780632+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:31:36.7809595+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:31:51.780658+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:32:06.7807451+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:32:21.780539+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:32:36.7809482+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:32:51.7808872+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:33:06.7806735+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:33:21.7808011+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:33:36.7805144+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:33:51.7806573+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:34:06.7806296+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:34:21.7809603+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:34:36.7809486+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:34:51.7809264+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:35:06.7808798+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:35:21.7809549+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:35:36.7808695+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:35:51.7808405+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:36:06.7805888+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:36:21.7809152+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:36:36.7807127+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:36:51.7808315+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:37:06.7806348+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:37:21.7807887+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:37:36.7806364+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:37:51.7807701+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:38:06.7806134+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:38:21.7805505+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:38:36.780748+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:38:51.7808456+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:39:06.7807338+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:39:21.7804845+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:39:36.7808397+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:39:51.7805326+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:40:06.7806381+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:40:21.7806405+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:40:36.780533+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:40:51.78097+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:41:06.7805388+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:41:21.7807726+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:41:36.7808274+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:41:51.7806496+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:42:06.7808033+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:42:21.7806087+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:42:36.7808443+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:42:51.780873+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:43:06.7809647+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:43:21.7809864+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:43:36.780549+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:43:51.7807921+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:44:06.7809322+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:44:21.7807209+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:44:36.7806256+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:44:51.7804971+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:45:06.7805542+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:45:21.7808558+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:45:36.7805981+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:45:51.7805109+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:46:06.7808077+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:46:21.7805621+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:46:36.7808223+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:46:51.7809826+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:47:06.7811235+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:47:21.7806836+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:47:36.7805269+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:47:51.7807618+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:48:06.7809895+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:48:21.7806835+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:48:36.7809109+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:48:51.7806727+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:49:06.7807109+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:49:21.7805731+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:49:36.7805277+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:49:51.7809651+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:50:06.7807885+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:50:21.7807994+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:50:36.7809964+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:50:51.7809446+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:51:06.7808269+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:51:21.7806027+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:51:36.7807617+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:51:51.7805332+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:52:06.7808455+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:52:21.7807873+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:52:36.7810025+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:52:51.7810075+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:53:06.780633+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:53:21.7808194+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:53:36.7808368+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:53:51.7809095+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:54:06.780908+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:54:21.7809328+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:54:36.7805028+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:54:51.7809636+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:55:06.7807049+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:55:21.7807934+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:55:36.7808432+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:55:51.7808561+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:56:06.7806548+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:56:21.7808595+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:56:36.7808961+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:56:51.7809426+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:57:06.7808729+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:57:21.7809711+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:57:36.7806372+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:57:51.7806474+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:58:06.7807294+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:58:21.7809145+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:58:36.7807428+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:58:51.7810146+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:59:06.7809726+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:59:21.7809292+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:59:36.7808207+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:59:51.7808206+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:00:06.7810414+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:00:21.7806636+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:00:36.7808887+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:00:51.7808629+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:01:06.7811312+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:01:21.7809181+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:01:36.780835+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:01:51.7810003+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:02:06.7809427+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:02:21.7807152+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:02:36.7809723+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:02:51.7807358+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:03:06.7809818+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:03:21.7806381+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:03:36.7809405+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:03:51.7809849+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:04:06.7807333+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:04:21.7808065+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:04:36.7808215+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:04:51.781114+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:05:06.7814447+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:05:21.7808413+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:05:36.7809712+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:05:51.7809907+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:06:06.7807229+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:06:21.7809333+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:06:36.7806712+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:06:51.7807918+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:07:06.7806039+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:07:21.7806177+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:07:36.7805792+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:07:51.7806516+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:08:06.7809222+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:08:21.7809556+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:08:36.780577+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:08:51.781026+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:09:06.7805565+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:09:21.7809168+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:09:36.7808158+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:09:51.7809959+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:10:06.780957+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:10:21.7807381+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:10:36.7808539+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:10:51.7808302+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:11:06.7805978+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:11:21.7806883+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:11:36.780743+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:11:51.7808268+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:12:06.7808803+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:12:21.7807637+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:12:36.7808338+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:12:51.7808594+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:13:06.7804992+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:13:21.78093+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:13:36.7807222+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:13:51.7807549+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:14:06.7806514+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:14:21.7808619+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:14:36.7807072+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:14:51.7805731+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:15:06.7809831+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:15:21.7809425+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:15:36.7805949+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:15:51.7809141+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:16:06.7808467+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:16:21.7809804+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:16:36.7805721+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:16:51.7806442+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:17:06.7808879+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:17:21.7805069+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:17:36.7804966+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:17:51.7809574+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:18:06.7806235+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:18:21.7806768+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:18:36.7808343+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:18:51.7808176+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:19:06.7807748+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:19:21.7807829+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:19:36.7805575+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:19:51.7809008+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:19:58.2156855+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/40frneir/file_stream\": EOF"}
{"time":"2025-09-07T21:20:06.7804964+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:20:21.7806161+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:20:36.7806803+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
