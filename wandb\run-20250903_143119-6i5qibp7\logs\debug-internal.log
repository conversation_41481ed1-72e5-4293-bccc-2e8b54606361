{"time":"2025-09-03T14:31:20.0613455+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-09-03T14:31:21.2951166+08:00","level":"INFO","msg":"stream: created new stream","id":"6i5qibp7"}
{"time":"2025-09-03T14:31:21.2951166+08:00","level":"INFO","msg":"stream: started","id":"6i5qibp7"}
{"time":"2025-09-03T14:31:21.295661+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"6i5qibp7"}
{"time":"2025-09-03T14:31:21.295661+08:00","level":"INFO","msg":"handler: started","stream_id":"6i5qibp7"}
{"time":"2025-09-03T14:31:21.295661+08:00","level":"INFO","msg":"sender: started","stream_id":"6i5qibp7"}
{"time":"2025-09-03T14:31:21.9871192+08:00","level":"ERROR","msg":"git repo not found","error":"repository does not exist"}
{"time":"2025-09-03T15:05:08.3685379+08:00","level":"ERROR","msg":"HTTP error","status":404,"method":"POST","url":"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/6i5qibp7/file_stream"}
{"time":"2025-09-03T15:05:08.3685379+08:00","level":"ERROR+4","msg":"filestream: fatal error: filestream: failed to upload: 404 Not Found path=files/xzk8559/recLSTM-bridge/6i5qibp7/file_stream: {\"error\":\"run recLSTM-bridge/6i5qibp7 not found while streaming file\"}"}
