Shape of input  per step: torch.Size([148, 5])
Shape of output per step: torch.Size([148, 2])
Dataset length: 423180
Shape of input  per step: torch.Size([148, 5])
Shape of output per step: torch.Size([148, 2])
Dataset length: 396954
TrainingRNN(
  (head): GRU(4, 256, batch_first=True)
  (hidden): ModuleList(
    (0-2): 3 x GRU(256, 256, batch_first=True)
  )
  (fc): Sequential(
    (0): Linear(in_features=256, out_features=256, bias=True)
    (1): Tanh()
    (2): Linear(in_features=256, out_features=2, bias=True)
  )
)
1451778 paramerters in total
0	0.30253389	0.29456100	0.00060	4.55
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.80 seconds
Model Performance:
- Correlation: -0.00777
- Root Mean Squared Error: 6.74 mm
- Peak Error: 75.207 %
D:\anaconda3\envs\torch22\lib\site-packages\torch\optim\lr_scheduler.py:143: UserWarning: Detected call of `lr_scheduler.step()` before `optimizer.step()`. In PyTorch 1.1.0 and later, you should call them in the opposite order: `optimizer.step()` before `lr_scheduler.step()`.  Failure to do this will result in PyTorch skipping the first value of the learning rate schedule. See more details at https://pytorch.org/docs/stable/optim.html#how-to-adjust-learning-rate
  warnings.warn("Detected call of `lr_scheduler.step()` before `optimizer.step()`. "
1	0.02787513	0.02674706	0.00060	33.06
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.91 seconds
Model Performance:
- Correlation: 0.63054
- Root Mean Squared Error: 3.69 mm
- Peak Error: 44.821 %
2	0.00956235	0.01114142	0.00059	61.75
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.64 seconds
Model Performance:
- Correlation: 0.85899
- Root Mean Squared Error: 2.51 mm
- Peak Error: 12.115 %
3	0.00270174	0.00533152	0.00059	90.04
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.06 seconds
Model Performance:
- Correlation: 0.89928
- Root Mean Squared Error: 1.96 mm
- Peak Error: 8.467 %
4	0.00718016	0.00350980	0.00059	118.85
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.08 seconds
Model Performance:
- Correlation: 0.90754
- Root Mean Squared Error: 1.87 mm
- Peak Error: 7.270 %
5	0.00228065	0.00222260	0.00059	146.62
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.38 seconds
Model Performance:
- Correlation: 0.91523
- Root Mean Squared Error: 1.76 mm
- Peak Error: 8.196 %
6	0.00153451	0.00258005	0.00058	177.51
7	0.00129813	0.00184898	0.00058	189.73
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 20.94 seconds
Model Performance:
- Correlation: 0.93884
- Root Mean Squared Error: 1.47 mm
- Peak Error: 7.704 %
8	0.00079495	0.00137897	0.00058	223.62
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.99 seconds
Model Performance:
- Correlation: 0.95334
- Root Mean Squared Error: 1.28 mm
- Peak Error: 10.608 %
9	0.00099739	0.00073395	0.00057	256.41
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 22.15 seconds
Model Performance:
- Correlation: 0.92607
- Root Mean Squared Error: 1.69 mm
- Peak Error: 6.284 %
10	0.00070015	0.00082996	0.00057	291.02
11	0.00057254	0.00077082	0.00057	303.19
12	0.00140042	0.00128953	0.00057	315.22
13	0.00032249	0.00053483	0.00056	328.21
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 20.45 seconds
Model Performance:
- Correlation: 0.90388
- Root Mean Squared Error: 1.94 mm
- Peak Error: 7.338 %
14	0.00071149	0.00068455	0.00056	365.52
15	0.00036879	0.00040812	0.00056	379.86
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.42 seconds
Model Performance:
- Correlation: 0.91380
- Root Mean Squared Error: 1.75 mm
- Peak Error: 11.159 %
16	0.00078015	0.00044558	0.00056	414.08
17	0.00055882	0.00062709	0.00055	427.66
18	0.00032683	0.00039485	0.00055	442.70
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 22.11 seconds
Model Performance:
- Correlation: 0.11617
- Root Mean Squared Error: 24656.71 mm
- Peak Error: 169098.506 %
19	0.00059242	0.00063554	0.00055	477.70
20	0.00023357	0.00029072	0.00055	493.36
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.66 seconds
Model Performance:
- Correlation: 0.95436
- Root Mean Squared Error: 1.31 mm
- Peak Error: 7.271 %
21	0.00039512	0.00035324	0.00054	526.51
22	0.00029281	0.00041541	0.00054	541.02
23	0.00020126	0.00031923	0.00054	552.73
24	0.00020920	0.00031378	0.00054	564.26
25	0.00033585	0.00035820	0.00053	575.75
26	0.00031927	0.00026126	0.00053	587.29
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.12 seconds
Model Performance:
- Correlation: 0.95816
- Root Mean Squared Error: 1.29 mm
- Peak Error: 10.008 %
27	0.00026038	0.00026532	0.00053	618.45
28	0.00040944	0.00036253	0.00053	631.25
29	0.00032981	0.00045816	0.00052	643.37
30	0.00042529	0.00032335	0.00052	655.41
31	0.00036276	0.00027821	0.00052	667.44
32	0.00058371	0.00045628	0.00052	679.51
33	0.00042309	0.00033707	0.00052	691.43
34	0.00056328	0.00053711	0.00051	703.97
35	0.00041655	0.00040382	0.00051	716.28
36	0.00017021	0.00023430	0.00051	728.44
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 20.36 seconds
Model Performance:
- Correlation: 0.97549
- Root Mean Squared Error: 1.07 mm
- Peak Error: 5.523 %
37	0.00023984	0.00023530	0.00051	762.86
38	0.00029074	0.00026176	0.00050	776.85
39	0.00019426	0.00020493	0.00050	790.18
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 20.45 seconds
Model Performance:
- Correlation: 0.97311
- Root Mean Squared Error: 1.17 mm
- Peak Error: 9.502 %
40	0.00021942	0.00020551	0.00050	823.20
41	0.00024482	0.00029319	0.00050	834.89
42	0.00040867	0.00019648	0.00050	846.78
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.20 seconds
Model Performance:
- Correlation: 0.75735
- Root Mean Squared Error: 6100.26 mm
- Peak Error: 37930.728 %
43	0.00021157	0.00020303	0.00049	877.91
44	0.00042304	0.00015084	0.00049	895.46
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 20.38 seconds
Model Performance:
- Correlation: 0.92831
- Root Mean Squared Error: 2.08 mm
- Peak Error: 24.102 %
45	0.00047945	0.00019333	0.00049	934.22
46	0.00033004	0.00032164	0.00049	951.73
47	0.00024779	0.00022480	0.00049	969.72
48	0.00008096	0.00015154	0.00048	989.95
49	0.00006696	0.00009881	0.00048	1009.09
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 28.29 seconds
Model Performance:
- Correlation: 0.92845
- Root Mean Squared Error: 1.73 mm
- Peak Error: 18.016 %
50	0.00013848	0.00017702	0.00048	1058.11
51	0.00015608	0.00019630	0.00048	1077.36
52	0.00020667	0.00013227	0.00048	1097.28
53	0.00020950	0.00009688	0.00047	1117.98
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 25.35 seconds
Model Performance:
- Correlation: 0.55496
- Root Mean Squared Error: 10932.88 mm
- Peak Error: 94869.081 %
54	0.00037207	0.00039433	0.00047	1162.51
55	0.00020361	0.00022167	0.00047	1180.59
56	0.00011001	0.00015458	0.00047	1198.59
57	0.00015640	0.00013903	0.00047	1215.11
58	0.00033515	0.00011090	0.00047	1233.42
59	0.00007356	0.00012173	0.00046	1251.47
60	0.00017608	0.00013708	0.00046	1269.63
61	0.00007449	0.00012377	0.00046	1287.53
62	0.00012906	0.00017596	0.00046	1305.35
63	0.00010691	0.00010292	0.00046	1323.46
64	0.00007603	0.00014549	0.00045	1341.65
65	0.00036857	0.00036084	0.00045	1355.08
66	0.00004783	0.00009815	0.00045	1366.96
67	0.00008817	0.00012384	0.00045	1378.91
68	0.00021039	0.00009724	0.00045	1390.70
69	0.00017007	0.00009049	0.00045	1402.58
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.31 seconds
Model Performance:
- Correlation: 0.92363
- Root Mean Squared Error: 1.82 mm
- Peak Error: 15.994 %
70	0.00033026	0.00010928	0.00044	1433.28
71	0.00016803	0.00014281	0.00044	1445.14
72	0.00029762	0.00010762	0.00044	1456.96
73	0.00029253	0.00012725	0.00044	1468.78
74	0.00009083	0.00009756	0.00044	1480.73
75	0.00010072	0.00010821	0.00044	1492.52
76	0.00005791	0.00011316	0.00043	1504.28
77	0.00009583	0.00012627	0.00043	1516.06
78	0.00011591	0.00014177	0.00043	1527.74
79	0.00011395	0.00012754	0.00043	1539.50
80	0.00011521	0.00010880	0.00043	1550.94
81	0.00010788	0.00010301	0.00043	1562.68
82	0.00010254	0.00010901	0.00043	1575.36
83	0.00008339	0.00011270	0.00042	1587.64
84	0.00011643	0.00008958	0.00042	1599.94
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.98 seconds
Model Performance:
- Correlation: 0.93442
- Root Mean Squared Error: 1.97 mm
- Peak Error: 17.658 %
85	0.00015077	0.00009990	0.00042	1632.95
86	0.00016485	0.00009946	0.00042	1645.14
87	0.00014015	0.00012969	0.00042	1656.98
88	0.00008788	0.00010301	0.00042	1668.51
89	0.00019277	0.00008741	0.00042	1679.93
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 21.83 seconds
Model Performance:
- Correlation: 0.88434
- Root Mean Squared Error: 2.39 mm
- Peak Error: 16.816 %
90	0.00015113	0.00012060	0.00041	1715.05
91	0.00009724	0.00011172	0.00041	1727.15
92	0.00006887	0.00013341	0.00041	1739.24
93	0.00010493	0.00010319	0.00041	1751.36
94	0.00015041	0.00016181	0.00041	1763.37
95	0.00005874	0.00009289	0.00041	1775.09
96	0.00007137	0.00008712	0.00041	1786.99
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.51 seconds
Model Performance:
- Correlation: 0.89130
- Root Mean Squared Error: 2.16 mm
- Peak Error: 11.970 %
97	0.00011160	0.00011080	0.00040	1817.88
98	0.00007051	0.00012425	0.00040	1829.51
99	0.00011245	0.00012523	0.00040	1841.23
100	0.00008932	0.00008429	0.00040	1852.87
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.17 seconds
Model Performance:
- Correlation: 0.15066
- Root Mean Squared Error: 7623.63 mm
- Peak Error: 90295.846 %
101	0.00007400	0.00008862	0.00040	1883.28
102	0.00007486	0.00009114	0.00040	1894.87
103	0.00011021	0.00013951	0.00040	1906.52
104	0.00016940	0.00017487	0.00039	1918.16
105	0.00004014	0.00009435	0.00039	1929.55
106	0.00013228	0.00010266	0.00039	1940.95
107	0.00010996	0.00009234	0.00039	1952.45
108	0.00005833	0.00008886	0.00039	1964.71
109	0.00012790	0.00012429	0.00039	1976.33
110	0.00011688	0.00009601	0.00039	1988.43
111	0.00010622	0.00009976	0.00039	2000.10
112	0.00008020	0.00008544	0.00038	2011.83
113	0.00023136	0.00011607	0.00038	2023.97
114	0.00004172	0.00007545	0.00038	2035.76
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.19 seconds
Model Performance:
- Correlation: 0.88624
- Root Mean Squared Error: 2.20 mm
- Peak Error: 15.603 %
115	0.00010260	0.00014777	0.00038	2067.42
116	0.00012464	0.00010145	0.00038	2079.52
117	0.00019207	0.00008077	0.00038	2091.55
118	0.00007212	0.00008751	0.00038	2103.59
119	0.00009657	0.00009297	0.00038	2115.83
120	0.00005887	0.00009380	0.00037	2127.82
121	0.00009975	0.00007902	0.00037	2139.87
122	0.00005102	0.00006915	0.00037	2151.70
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.81 seconds
Model Performance:
- Correlation: 0.85322
- Root Mean Squared Error: 2.66 mm
- Peak Error: 10.768 %
123	0.00008703	0.00009599	0.00037	2184.48
124	0.00011885	0.00010731	0.00037	2196.65
125	0.00011773	0.00008987	0.00037	2208.68
126	0.00008147	0.00007792	0.00037	2220.92
127	0.00008077	0.00007336	0.00037	2232.89
128	0.00008768	0.00009118	0.00037	2244.91
129	0.00012013	0.00013677	0.00036	2256.85
130	0.00004485	0.00006469	0.00036	2268.66
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.81 seconds
Model Performance:
- Correlation: 0.88455
- Root Mean Squared Error: 2.41 mm
- Peak Error: 23.478 %
131	0.00012796	0.00006682	0.00036	2301.25
132	0.00012218	0.00008981	0.00036	2313.32
133	0.00004017	0.00008554	0.00036	2325.32
134	0.00005044	0.00009317	0.00036	2337.15
135	0.00005734	0.00009314	0.00036	2349.02
136	0.00004446	0.00007672	0.00036	2360.60
137	0.00012204	0.00007351	0.00036	2372.31
138	0.00009014	0.00008895	0.00036	2383.80
139	0.00004994	0.00009032	0.00035	2395.78
140	0.00011255	0.00007710	0.00035	2407.59
141	0.00015717	0.00008911	0.00035	2419.42
142	0.00010520	0.00007723	0.00035	2431.25
143	0.00009863	0.00007151	0.00035	2443.47
144	0.00009617	0.00008286	0.00035	2455.40
145	0.00011155	0.00009651	0.00035	2467.35
146	0.00009847	0.00009264	0.00035	2479.38
147	0.00005220	0.00006421	0.00035	2491.61
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.68 seconds
Model Performance:
- Correlation: 0.81861
- Root Mean Squared Error: 3.27 mm
- Peak Error: 19.904 %
148	0.00005906	0.00007354	0.00034	2524.05
149	0.00007389	0.00007502	0.00034	2536.37
150	0.00011519	0.00008107	0.00034	2549.52
151	0.00003063	0.00007160	0.00034	2562.13
152	0.00005362	0.00007377	0.00034	2574.56
153	0.00012140	0.00007894	0.00034	2586.63
154	0.00005219	0.00008082	0.00034	2599.15
155	0.00011313	0.00010669	0.00034	2611.35
156	0.00005316	0.00005907	0.00034	2623.13
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 20.96 seconds
Model Performance:
- Correlation: 0.87679
- Root Mean Squared Error: 2.31 mm
- Peak Error: 15.510 %
157	0.00006572	0.00008218	0.00034	2657.48
158	0.00006287	0.00007421	0.00034	2669.11
159	0.00007543	0.00007237	0.00033	2682.31
160	0.00007963	0.00006127	0.00033	2696.10
161	0.00006638	0.00006552	0.00033	2712.71
162	0.00004278	0.00006928	0.00033	2726.73
163	0.00006933	0.00006864	0.00033	2738.64
164	0.00005236	0.00005898	0.00033	2750.48
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.89 seconds
Model Performance:
- Correlation: 0.80902
- Root Mean Squared Error: 2.90 mm
- Peak Error: 13.104 %
165	0.00009387	0.00008058	0.00033	2781.69
166	0.00003007	0.00005943	0.00033	2793.47
167	0.00009053	0.00008572	0.00033	2805.59
168	0.00008222	0.00008598	0.00033	2817.60
169	0.00007914	0.00007944	0.00033	2829.69
170	0.00007419	0.00006564	0.00032	2841.87
171	0.00006427	0.00006114	0.00032	2854.41
172	0.00011367	0.00007407	0.00032	2866.54
173	0.00010526	0.00006970	0.00032	2878.76
174	0.00018977	0.00017895	0.00032	2890.88
175	0.00008652	0.00007421	0.00032	2902.96
176	0.00007572	0.00006907	0.00032	2915.26
177	0.00005396	0.00005855	0.00032	2927.58
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 20.91 seconds
Model Performance:
- Correlation: 0.88400
- Root Mean Squared Error: 2.33 mm
- Peak Error: 22.543 %
178	0.00004810	0.00006725	0.00032	2961.20
179	0.00006733	0.00008627	0.00032	2973.21
180	0.00008213	0.00005972	0.00032	2985.46
181	0.00006034	0.00006812	0.00031	2997.38
182	0.00006197	0.00005913	0.00031	3008.93
183	0.00006003	0.00006542	0.00031	3020.41
184	0.00011469	0.00007655	0.00031	3032.30
185	0.00007614	0.00007823	0.00031	3044.20
186	0.00005880	0.00006849	0.00031	3056.09
187	0.00003567	0.00005883	0.00031	3067.96
188	0.00005879	0.00008855	0.00031	3079.86
189	0.00006031	0.00006552	0.00031	3091.74
190	0.00004596	0.00005968	0.00031	3103.55
191	0.00006315	0.00006191	0.00031	3115.29
192	0.00005183	0.00006697	0.00031	3127.24
193	0.00008706	0.00007257	0.00031	3139.01
194	0.00006005	0.00006945	0.00030	3150.94
195	0.00005690	0.00005694	0.00030	3162.60
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 22.42 seconds
Model Performance:
- Correlation: 0.86525
- Root Mean Squared Error: 2.35 mm
- Peak Error: 16.461 %
196	0.00007799	0.00008858	0.00030	3197.88
197	0.00003144	0.00006391	0.00030	3209.93
198	0.00006366	0.00006170	0.00030	3221.98
199	0.00005384	0.00005670	0.00030	3233.88
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 20.92 seconds
Model Performance:
- Correlation: 0.82075
- Root Mean Squared Error: 2.81 mm
- Peak Error: 14.889 %
200	0.00010904	0.00005870	0.00030	3267.29
201	0.00008822	0.00005675	0.00030	3279.48
202	0.00004522	0.00005757	0.00030	3291.59
203	0.00004427	0.00005754	0.00030	3303.89
204	0.00007854	0.00005660	0.00030	3315.60
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.74 seconds
Model Performance:
- Correlation: 0.84056
- Root Mean Squared Error: 2.65 mm
- Peak Error: 18.250 %
