Shape of input  per step: torch.Size([120, 3])
Shape of output per step: torch.Size([120, 1])
Dataset length: 417330
Shape of input  per step: torch.Size([120, 3])
Shape of output per step: torch.Size([120, 1])
Dataset length: 395199
TrainingLSTM(
  (head): GRU(2, 256, batch_first=True)
  (hidden): ModuleList(
    (0-2): 3 x GRU(256, 256, batch_first=True)
  )
  (fc): Sequential(
    (0): Linear(in_features=256, out_features=256, bias=True)
    (1): Tanh()
    (2): Linear(in_features=256, out_features=1, bias=True)
  )
)
1449985 paramerters in total
0	0.02911911	0.03145565	0.00100	3.94
Loading single model from epoch best...
Model weights loaded successfully.
Traceback (most recent call last):
  File "E:\Codes\recLSTM-bridge\train.py", line 161, in <module>
    run(args)
  File "E:\Codes\recLSTM-bridge\train.py", line 98, in run
    rec_results = run_prediction(
  File "E:\Codes\recLSTM-bridge\test.py", line 185, in run_prediction
    predictions, ground_truth, sequence_lengths = model(data_path)
  File "D:\anaconda3\envs\torch22\lib\site-packages\torch\nn\modules\module.py", line 1511, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "D:\anaconda3\envs\torch22\lib\site-packages\torch\nn\modules\module.py", line 1520, in _call_impl
    return forward_call(*args, **kwargs)
  File "E:\Codes\recLSTM-bridge\utils_model_fast.py", line 115, in forward
    history, ground_truth, sequence_lengths = self.pre_process(data_path)
  File "E:\Codes\recLSTM-bridge\utils_model_fast.py", line 248, in pre_process
    gt = load_test_data(data_path, self.scaler_path, self.args)
  File "E:\Codes\recLSTM-bridge\utils_model_fast.py", line 334, in load_test_data
    h = scaler.scale(h)[:, :1+pdim]
  File "E:\Codes\recLSTM-bridge\utils_data.py", line 100, in scale
    return f / self.max_abs_[np.newaxis, :, np.newaxis]
ValueError: operands could not be broadcast together with shapes (27,7,15000) (1,2,1)
