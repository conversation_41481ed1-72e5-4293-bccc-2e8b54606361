"""
Neural Network Models for Time Series Prediction
===============================================

This module contains neural network models for time series prediction tasks,
particularly focused on structural response prediction.

Two main model types are implemented:
1. TrainingLSTM: A basic LSTM model for training (simpler, used during training phase)
2. InferenceLSTM: An advanced LSTM model for autoregressive prediction (more complex, used during inference)

Author: Your Name
License: MIT
"""

import torch
import torch.nn as nn
import numpy as np
from SymmetrizedBlock import SymmetrizedBlock


class TrainingRNN(torch.nn.Module):
    def __init__(self, args, sym=True):
        super().__init__()
        # Model architecture parameters
        self.sym = sym
        self.story = args.pred_dim
        self.in_dim = args.pred_dim * 2  # acc + abs
        self.out_dim = args.pred_dim     # inc
        
        if sym:
            self.head = SymmetrizedBlock(self.in_dim, args.width, reverse=True)
            self.hidden = nn.ModuleList([
                SymmetrizedBlock(args.width, args.width) for _ in range(args.layers-1)
            ])
        else:
            self.head = nn.GRU(self.in_dim, args.width, 1, bias=True, batch_first=True)
            self.hidden = nn.ModuleList([
                nn.GRU(args.width, args.width, 1, bias=True, batch_first=True) for _ in range(args.layers-1)
            ])
        
        
        # Output layer
        # if y(T(x)) = -y(x), fc(y(T(x))) = fc(-y(x)) = -fc(y(x))
        self.fc = nn.Sequential(
            nn.Linear(args.width, args.width, bias=False if sym else True),
            nn.Tanh(),
            nn.Linear(args.width, self.out_dim, bias=False if sym else True),
        )

    def forward(self, x, h=None):
        y = torch.cat([
            x[:, :, :1].expand(-1, -1, self.story),
            x[:, :, 1:1+self.story],
        ], -1)
        
        # For symmetrized model, handle hidden states properly
        if self.sym:  # SymmetrizedBlock
            if h is not None:
                # For symmetry constraint, create h_neg = -h
                h_combined = torch.cat([h, -h], dim=1)
                y, h_out = self.head(y, h_combined)
            else:
                y, _ = self.head(y, h)
                h_out = None
        else:  # Regular GRU
            y, _ = self.head(y, h)
            h_out = None
            
        for layer in self.hidden:
            y, h_out = layer(y, h_out)
        
        y = self.fc(y)
        return y


class TrainingDenseLSTM(nn.Module):
    def __init__(self, args):
        super().__init__()
        self.growth_rate = getattr(args, 'growth_rate', args.width // 2)

        # Model architecture parameters
        self.story = args.pred_dim
        self.in_dim = args.pred_dim * 2
        self.out_dim = args.pred_dim
        
        self.head = SymmetrizedBlock(self.in_dim, args.width)
        
        self.hidden = nn.ModuleList()
        current_dim = args.width
        for i in range(args.layers - 1):
            block = SymmetrizedBlock(current_dim, self.growth_rate)
            self.hidden.append(block)
            current_dim += self.growth_rate
            
        final_hidden_dim = args.width + (args.layers - 1) * self.growth_rate
        self.fc = nn.Sequential(
            nn.Linear(final_hidden_dim, args.width, bias=False),
            nn.Tanh(),
            nn.Linear(args.width, self.out_dim, bias=False),
        )

    def forward(self, x):
        y = torch.cat([
            x[:, :, :1].expand(-1, -1, self.story),
            x[:, :, 1:1+self.story],
        ], -1)
        
        y_head, _ = self.head(x)
        
        features = [y_head]
        for layer in self.hidden:
            input_tensor = torch.cat(features, dim=-1)
            new_feature, _ = layer(input_tensor)
            features.append(new_feature)
            
        final_features = torch.cat(features, dim=-1)
        y = self.fc(final_features)
        
        return y