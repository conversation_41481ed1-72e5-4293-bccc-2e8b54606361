Shape of input  per step: torch.Size([120, 5])
Shape of output per step: torch.Size([120, 2])
Dataset length: 834660
Shape of input  per step: torch.Size([120, 5])
Shape of output per step: torch.Size([120, 2])
Dataset length: 395199
TrainingRNN(
  (head): SymmetrizedBlock(
    (rnn): GRU(4, 256, batch_first=True)
  )
  (hidden): ModuleList(
    (0-2): 3 x SymmetrizedBlock(
      (rnn): GRU(256, 256, batch_first=True)
    )
  )
  (fc): Sequential(
    (0): Linear(in_features=256, out_features=256, bias=False)
    (1): Tanh()
    (2): Linear(in_features=256, out_features=2, bias=False)
  )
)
1451520 paramerters in total
0	0.00051335	0.00049471	0.00060	7.11
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.52 seconds
Model Performance:
- Correlation: -0.10381
- Root Mean Squared Error: 1.63 mm
- Peak Error: 98.565 %
1	0.00022140	0.00025372	0.00060	44.14
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.55 seconds
Model Performance:
- Correlation: 0.69178
- Root Mean Squared Error: 1.44 mm
- Peak Error: 30.679 %
2	0.00116612	0.00026871	0.00059	81.71
3	0.00017701	0.00032060	0.00059	101.65
4	0.00009562	0.00028911	0.00059	121.48
5	0.00015794	0.00026753	0.00059	141.00
6	0.00013548	0.00020124	0.00058	160.32
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.38 seconds
Model Performance:
- Correlation: 0.70215
- Root Mean Squared Error: 1.39 mm
- Peak Error: 34.271 %
7	0.00060867	0.00020949	0.00058	199.18
8	0.00006229	0.00027333	0.00058	217.95
9	0.00007131	0.00019985	0.00057	236.77
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.58 seconds
Model Performance:
- Correlation: 0.48315
- Root Mean Squared Error: 3.21 mm
- Peak Error: 82.585 %
10	0.00017908	0.00015572	0.00057	274.90
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.56 seconds
Model Performance:
- Correlation: 0.51715
- Root Mean Squared Error: 2.09 mm
- Peak Error: 27.234 %
11	0.00004634	0.00014203	0.00057	312.01
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.22 seconds
Model Performance:
- Correlation: 0.75025
- Root Mean Squared Error: 1.30 mm
- Peak Error: 25.861 %
12	0.00017597	0.00014706	0.00057	348.61
13	0.00015821	0.00013321	0.00056	367.70
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.57 seconds
Model Performance:
- Correlation: 0.81251
- Root Mean Squared Error: 1.24 mm
- Peak Error: 29.363 %
14	0.00004932	0.00011252	0.00056	404.71
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.27 seconds
Model Performance:
- Correlation: 0.00296
- Root Mean Squared Error: 323313.67 mm
- Peak Error: 11909194.293 %
15	0.00013638	0.00014843	0.00056	441.34
16	0.00002627	0.00010996	0.00056	460.19
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.87 seconds
Model Performance:
- Correlation: 0.34497
- Root Mean Squared Error: 27.37 mm
- Peak Error: 4597.808 %
17	0.00002422	0.00009854	0.00055	497.61
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.17 seconds
Model Performance:
- Correlation: 0.37913
- Root Mean Squared Error: 2.95 mm
- Peak Error: 47.069 %
18	0.00118616	0.00010578	0.00055	534.71
19	0.00002676	0.00009896	0.00055	554.19
20	0.00003069	0.00008359	0.00055	573.45
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.66 seconds
Model Performance:
- Correlation: -0.00618
- Root Mean Squared Error: 38841.19 mm
- Peak Error: 2707389.245 %
21	0.00022840	0.00010395	0.00054	611.48
22	0.00001404	0.00008327	0.00054	630.58
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.72 seconds
Model Performance:
- Correlation: 0.15897
- Root Mean Squared Error: 7017.75 mm
- Peak Error: 459880.863 %
23	0.00004128	0.00013218	0.00054	667.59
24	0.00009245	0.00008755	0.00054	686.38
25	0.00002941	0.00007541	0.00053	705.15
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.39 seconds
Model Performance:
- Correlation: -0.00564
- Root Mean Squared Error: 198664.87 mm
- Peak Error: 8679347.034 %
26	0.00001178	0.00008609	0.00053	742.12
27	0.00032874	0.00010412	0.00053	760.87
28	0.00003599	0.00004830	0.00053	779.65
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.76 seconds
Model Performance:
- Correlation: 0.00575
- Root Mean Squared Error: 224850.56 mm
- Peak Error: 11011132.464 %
29	0.00001065	0.00009980	0.00052	816.92
30	0.00001564	0.00008870	0.00052	835.81
31	0.00002133	0.00015857	0.00052	854.54
32	0.00009174	0.00011202	0.00052	873.87
33	0.00005835	0.00006743	0.00052	894.23
34	0.00002112	0.00011623	0.00051	914.88
35	0.00001435	0.00013301	0.00051	938.34
36	0.00001718	0.00008824	0.00051	962.97
37	0.00008683	0.00009149	0.00051	985.64
38	0.00000768	0.00012283	0.00050	1004.61
39	0.00000619	0.00009167	0.00050	1023.77
40	0.00035425	0.00008278	0.00050	1042.51
41	0.00004371	0.00010569	0.00050	1061.27
42	0.00005982	0.00010277	0.00050	1080.26
43	0.00003211	0.00013597	0.00049	1099.43
44	0.00003035	0.00011629	0.00049	1118.70
45	0.00003169	0.00009228	0.00049	1138.23
46	0.00011456	0.00009167	0.00049	1158.10
47	0.00013694	0.00007875	0.00049	1177.28
48	0.00000805	0.00013385	0.00048	1196.08
49	0.00002618	0.00011046	0.00048	1214.85
50	0.00012271	0.00011829	0.00048	1233.90
51	0.00001253	0.00010661	0.00048	1252.75
