#%%
import torch
import numpy as np
import matplotlib.pyplot as plt
# from utils_model import InferenceLSTM, update_state_dict, load_args
from utils_model_sym import InferenceLSTM, update_state_dict, load_args


# Part 1: Evaluation Functions
# ===========================

def evaluate(predictions, ground_truth, sequence_lengths, scaler, plot_history=False):
    """
    Evaluate model predictions against ground truth.
    
    This function calculates correlation coefficients and root mean squared error
    between model predictions and ground truth data.
    
    Parameters:
    -----------
    predictions : numpy.ndarray
        Model predictions with shape (batch_size, time_steps, features)
    ground_truth : numpy.ndarray
        Ground truth data with shape (batch_size, time_steps, features)
    sequence_lengths : list
        List of sequence lengths for each example
    scaler : numpy.ndarray
        Scaling factors for rescaling data
    plot_history : bool
        Whether to plot prediction results
        
    Returns:
    --------
    tuple
        (correlation, root_mean_squared_error)
    """
    # ground_truth = ground_truth[:,:-10]
    # predictions = predictions[:,:-10]
    
    num_examples, num_floors = ground_truth.shape[0], ground_truth.shape[2]-1
    correlations = np.zeros((num_examples, num_floors))
    rmse_values = np.zeros((num_examples, num_floors))
    peak_error = np.zeros((num_examples, num_floors))
    
    # Rescale data to original scale
    ground_truth = ground_truth * scaler[np.newaxis, np.newaxis, :ground_truth.shape[-1]]
    predictions = predictions * scaler[np.newaxis, np.newaxis, :ground_truth.shape[-1]]
    
    # Calculate metrics for each example and floor
    for example_idx in range(num_examples):
        for floor_idx in range(num_floors):
            gt_values = ground_truth[example_idx, :sequence_lengths[example_idx], floor_idx+1]
            pred_values = predictions[example_idx, :sequence_lengths[example_idx], floor_idx+1]
            
            correlations[example_idx, floor_idx] = np.corrcoef(gt_values, pred_values)[1][0]  # Correlation coefficient
            rmse_values[example_idx, floor_idx] = np.sqrt(((gt_values - pred_values)**2).mean())  # Root Mean Squared Error
            
            pv1 = np.abs(gt_values).max()
            pv2 = np.abs(pred_values).max()
            peak_error[example_idx, floor_idx] = np.abs(1 - pv2/pv1)
    
    # Calculate mean metrics
    mean_correlation = correlations.mean()
    mean_rmse = rmse_values.mean()
    mean_peak_error = peak_error.mean()
    
    # Visualize results if requested
    if plot_history:
        visualize_predictions(ground_truth, predictions, sequence_lengths, mean_correlation, max_plots=30)
    
    return mean_correlation, mean_rmse, mean_peak_error


def visualize_predictions(ground_truth, predictions, sequence_lengths, mean_correlation=None, max_plots=10):
    """
    Visualize model predictions against ground truth.
    
    Creates plots comparing model predictions to ground truth values,
    displaying correlation coefficients for each example.
    
    Parameters:
    -----------
    ground_truth : numpy.ndarray
        Ground truth data with shape (batch_size, time_steps, features)
    predictions : numpy.ndarray
        Model predictions with shape (batch_size, time_steps, features)
    sequence_lengths : list
        List of sequence lengths for each example
    mean_correlation : float
        Mean correlation coefficient across all examples
    max_plots : int
        Maximum number of examples to plot
    """
    num_plots = min(ground_truth.shape[0], max_plots)
    # Using tight_layout instead of constrained_layout for more control
    plt.figure(figsize=(12,8), dpi=120)
    nrow = num_plots//2 if num_plots%2==0 else num_plots//2+1
    ncol = 2
    
    for example_idx in range(num_plots):
        # Average displacements among all pier columns
        gt_total_displacement = ground_truth[example_idx, :sequence_lengths[example_idx], 1:].mean(-1)
        pred_total_displacement = predictions[example_idx, :sequence_lengths[example_idx], 1:].mean(-1)
        
        plt.subplot(nrow, ncol, example_idx+1)
        plt.plot(gt_total_displacement, linewidth=0.75, color='red', alpha=0.6, label='Ground Truth')
        plt.plot(pred_total_displacement, linewidth=0.75, color='green', alpha=0.6, label='Prediction')
        
        # Display correlation for this example
        example_correlation = np.corrcoef(gt_total_displacement, pred_total_displacement)[1][0]
        min_value = min(gt_total_displacement.min(), pred_total_displacement.min())
        plt.text(gt_total_displacement.shape[0]*0.7, min_value*0.8, f'Correlation: {example_correlation:.4f}', fontsize=8)
        
        if example_idx == 0:
            plt.legend(fontsize=8, ncol=2, loc='upper right', edgecolor='none')
            if mean_correlation is not None:
                plt.title(f'Model Predictions (Mean Correlation: {mean_correlation:.4f})', fontsize=10)
        
        # Hide x-axis ticks except for the last row plot
        if example_idx < ncol*(nrow-1):
            plt.xticks([], fontsize=8)
        else:
            plt.xlabel('T', fontsize=8)
            plt.xticks(fontsize=8)
        
        plt.yticks(fontsize=8)
        # plt.ylabel('D', fontsize=8)
        
        # Minimize whitespace for each subplot
        plt.tight_layout()
    
    # Adjust the spacing between subplots to make axes closer together
    plt.subplots_adjust(hspace=0.1)  # Reduced space between plots
    plt.show()


# Part 2: Model Prediction
# =====================

def run_prediction(
    model_path, data_path='data/', 
    past_history=50, local_sample_step=None,
    tta=False,
    sym=True,
    plot=True,
    best_epochs='best'
    ):
    """
    Demonstrate model prediction on test data.
    
    This function loads a pre-trained model, runs prediction on test data,
    and evaluates the results.
        
    Returns:
    --------
    tuple
        (correlation, root_mean_squared_error)
    """
    # Load scaler
    scaler = np.load(model_path + '/scaler.npy')
    
    # Load model configuration and create model
    model = InferenceLSTM(
        load_args(past_history, local_sample_step, path=model_path), tta, sym
        ).cuda()
    
    # Load model weights
    best_epochs = [best_epochs] if not isinstance(best_epochs, list) else best_epochs
    
    if len(best_epochs) == 1:
        epoch_to_load = best_epochs[0]
        print(f"Loading single model from epoch {epoch_to_load}...")
        state_dict = torch.load(f'{model_path}/{epoch_to_load}.pkl')
        model.load_state_dict(update_state_dict(state_dict, sym))
        
    elif len(best_epochs) > 1:
        print(f"Averaging models from epochs: {best_epochs}")
        all_dicts = [torch.load(f'{model_path}/{epoch}.pkl') for epoch in best_epochs]
        avg_dict = {}
        for k in all_dicts[0].keys():
            avg_dict[k] = torch.stack([d[k] for d in all_dicts]).mean(dim=0)
        model.load_state_dict(update_state_dict(avg_dict, sym))
    
    # Run prediction
    predictions, ground_truth, sequence_lengths = model(data_path)
    
    # Evaluate and optionally visualize
    r, rmse, pe = evaluate(predictions, ground_truth, sequence_lengths, scaler, plot)
    
    print(f"Model Performance:")
    print(f"- Correlation: {r:.5f}")
    print(f"- Root Mean Squared Error: {rmse*1000:.2f} mm")
    print(f"- Peak Error: {pe*100:.3f} %")
    return r, rmse, pe


# Main execution
# =============

if __name__ == "__main__":
    print("Running recLSTM Model Prediction")
    print("================================")
    # input:acc+abs | output:inc
    # r = max(inc)/max(abs) ≈ 0.2
    # ---------------------------------------
    model_path = 'wandb/run-20250902_173431-2h6wk7tc/files/' # 13-p-2*134 | best at 67
    # model_path = 'wandb/run-20250903_013115-95z6jjs7/files/' # 33-p-2*148 | best [55, 62, 67, 80]
    # model_path = 'wandb/run-20250903_123700-b165k9nf/files/' # 33-b-2*109 | best 54
    model_path = 'wandb/run-20250904_150938-6f5ycz4a/files/' # 13p-2*134  | best [9]
    # model_path = 'wandb/run-20250904_162936-5exm1wxt/files/' # 33b-3*120       | 33
    model_path = 'wandb/run-20250905_001040-flb54yaj/files/' # 33b-3*120-sym | 1417
    # model_path = 'wandb/run-20250907_183603-nbhq1rff/files/' # 33b-3*120 + pga-0.3 | x
    
    # Run prediction with visualization
    print(f"Using pre-trained model from: {model_path}")
    run_prediction(
        model_path, data_path='data/',
        past_history=120,
        sym=True,
        tta=False,
        plot=True,
        best_epochs=[1417]
        # best_epochs='best'
    ), 

    # %%
    # d0 = np.load('data/dis_bear/dis_bear_bridge_4_tra_50Hz_0.15g.npy')
    # d1 = np.load('data/dis_bear/dis_bear_bridge_4_tes_50Hz_0.15g.npy')
    # plt.figure(figsize=(8,2), dpi=300)
    # plt.scatter(np.arange(d0.shape[0]), np.argmax(np.abs(d0).max(1), 1), s=np.max(np.abs(d0).max(1), 1)*2000)
    # plt.show()
    # plt.figure(figsize=(8,2), dpi=300)
    # plt.scatter(np.arange(d1.shape[0]), np.argmax(np.abs(d1).max(1), 1), s=np.max(np.abs(d1).max(1), 1)*2000)
    # plt.show()

    #%% pier
    # d_pos = np.load('E:/Codes/opensees/run_fem_cloud/out/bridge-33-宜山路蒲汇塘桥_40_50.npz')['pier_displacements'][:,:6]
    # d_neg = np.load('E:/Codes/opensees/run_fem_cloud/out/bridge-33-宜山路蒲汇塘桥_40_50_neg.npz')['pier_displacements'][:,:6]
    # n = d_pos.shape[0]
    # i=2
    # # d_pos[i] = -d_neg[-i-1] = d_pos[n//2-i-1]
    # # d_neg[i] = -d_pos[-i-1] = d_neg[n//2-i-1] = -d_pos[n//2+i]
    # print(np.abs(d_pos[i]+d_neg[-i-1]).sum())
    # print(np.abs(d_neg[i]+d_pos[-i-1]).sum())
    # print(np.abs(d_pos[i]-d_pos[n//2-i-1]).sum())
    # print(np.abs(d_neg[i]+d_pos[n//2+i]).sum())
    #%% bear
    # d_pos = np.load('E:/Codes/opensees/run_fem_cloud/out/bridge-33-宜山路蒲汇塘桥_40_50.npz')['bearing_displacements'][:,:6]
    # d_neg = np.load('E:/Codes/opensees/run_fem_cloud/out/bridge-33-宜山路蒲汇塘桥_40_50_neg.npz')['bearing_displacements'][:,:6]

    # d_pos = np.concatenate([d_pos[-2:-1], d_pos[:-2], d_pos[-1:]], axis=0)
    # d_neg = np.concatenate([d_neg[-2:-1], d_neg[:-2], d_neg[-1:]], axis=0)
    # n = d_pos.shape[0]
    # i=2
    # # d_pos[i] = -d_neg[-i-1]
    # # d_neg[i] = -d_pos[-i-1]
    # print(np.abs(d_pos[i]+d_neg[-i-1]).sum())
    # print(np.abs(d_neg[i]+d_pos[-i-1]).sum())
    
# %%
