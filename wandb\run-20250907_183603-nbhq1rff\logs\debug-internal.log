{"time":"2025-09-07T18:36:04.0758407+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-09-07T18:36:08.0032208+08:00","level":"INFO","msg":"stream: created new stream","id":"nbhq1rff"}
{"time":"2025-09-07T18:36:08.0032208+08:00","level":"INFO","msg":"stream: started","id":"nbhq1rff"}
{"time":"2025-09-07T18:36:08.0032208+08:00","level":"INFO","msg":"handler: started","stream_id":"nbhq1rff"}
{"time":"2025-09-07T18:36:08.0032208+08:00","level":"INFO","msg":"sender: started","stream_id":"nbhq1rff"}
{"time":"2025-09-07T18:36:08.0032208+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"nbhq1rff"}
{"time":"2025-09-07T18:36:08.5197496+08:00","level":"ERROR","msg":"git repo not found","error":"repository does not exist"}
{"time":"2025-09-07T18:41:14.3941204+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": EOF"}
{"time":"2025-09-07T18:41:34.7973856+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": EOF"}
{"time":"2025-09-07T18:47:03.9938826+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": unexpected EOF"}
{"time":"2025-09-07T18:48:34.4040729+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": EOF"}
{"time":"2025-09-07T18:49:42.3058437+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": unexpected EOF"}
{"time":"2025-09-07T18:58:06.1040181+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": EOF"}
{"time":"2025-09-07T19:04:18.3691604+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": EOF"}
{"time":"2025-09-07T19:12:17.9106655+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": EOF"}
{"time":"2025-09-07T19:19:14.0836895+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": unexpected EOF"}
{"time":"2025-09-07T19:23:02.9262783+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": EOF"}
{"time":"2025-09-07T19:29:16.6026609+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": unexpected EOF"}
{"time":"2025-09-07T19:32:12.8758222+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": EOF"}
{"time":"2025-09-07T19:44:14.4480772+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": unexpected EOF"}
{"time":"2025-09-07T19:55:04.9399745+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": EOF"}
{"time":"2025-09-07T20:03:14.9954207+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": EOF"}
{"time":"2025-09-07T20:06:25.6902174+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": EOF"}
{"time":"2025-09-07T20:14:15.9264695+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": EOF"}
{"time":"2025-09-07T20:17:28.9081018+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": EOF"}
{"time":"2025-09-07T20:19:14.5701452+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": unexpected EOF"}
{"time":"2025-09-07T20:20:07.1531842+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": unexpected EOF"}
{"time":"2025-09-07T20:20:45.8911301+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": EOF"}
{"time":"2025-09-07T20:21:15.9316151+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": EOF"}
{"time":"2025-09-07T20:48:36.4817389+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": unexpected EOF"}
{"time":"2025-09-07T20:58:07.3187367+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": EOF"}
{"time":"2025-09-07T21:08:21.4951849+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": unexpected EOF"}
{"time":"2025-09-07T21:11:42.8794542+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/nbhq1rff/file_stream\": EOF"}
