Shape of input  per step: torch.Size([109, 5])
Shape of output per step: torch.Size([109, 2])
Dataset length: 410400
Shape of input  per step: torch.Size([109, 5])
Shape of output per step: torch.Size([109, 2])
Dataset length: 393120
TrainingLSTM(
  (head): GRU(4, 256, batch_first=True)
  (hidden): ModuleList(
    (0-2): 3 x GRU(256, 256, batch_first=True)
  )
  (fc): Sequential(
    (0): Linear(in_features=256, out_features=256, bias=True)
    (1): Tanh()
    (2): Linear(in_features=256, out_features=2, bias=True)
  )
)
1451778 paramerters in total
0	0.96854627	0.96008521	0.00100	3.90
Prediction time: 12.61 seconds
Model Performance:
- Correlation: -0.15347
- Root Mean Squared Error: 1397.82 mm
- Peak Error: 2547.781 %
D:\anaconda3\envs\torch22\lib\site-packages\torch\optim\lr_scheduler.py:143: UserWarning: Detected call of `lr_scheduler.step()` before `optimizer.step()`. In PyTorch 1.1.0 and later, you should call them in the opposite order: `optimizer.step()` before `lr_scheduler.step()`.  Failure to do this will result in PyTorch skipping the first value of the learning rate schedule. See more details at https://pytorch.org/docs/stable/optim.html#how-to-adjust-learning-rate
  warnings.warn("Detected call of `lr_scheduler.step()` before `optimizer.step()`. "
1	0.04070273	0.03125969	0.00100	26.30
Prediction time: 11.98 seconds
Model Performance:
- Correlation: 0.14937
- Root Mean Squared Error: 109.17 mm
- Peak Error: 199.209 %
2	0.01158415	0.01366017	0.00099	47.91
Prediction time: 11.91 seconds
Model Performance:
- Correlation: -0.10931
- Root Mean Squared Error: 198.12 mm
- Peak Error: 513.034 %
3	0.00724434	0.01055579	0.00099	69.40
Prediction time: 11.62 seconds
Model Performance:
- Correlation: -0.15876
- Root Mean Squared Error: 7694.69 mm
- Peak Error: 19128.853 %
4	0.01390725	0.01353400	0.00098	90.44
5	0.00533512	0.00740684	0.00098	99.51
Prediction time: 11.53 seconds
Model Performance:
- Correlation: -0.15156
- Root Mean Squared Error: 5661.34 mm
- Peak Error: 14869.437 %
6	0.00949524	0.00621615	0.00097	120.61
Prediction time: 11.47 seconds
Model Performance:
- Correlation: -0.17182
- Root Mean Squared Error: 5654.39 mm
- Peak Error: 14973.088 %
7	0.00463829	0.00593182	0.00097	141.83
Prediction time: 11.51 seconds
Model Performance:
- Correlation: -0.02756
- Root Mean Squared Error: 3606.12 mm
- Peak Error: 12391.001 %
8	0.00500386	0.00536242	0.00096	163.03
Prediction time: 11.39 seconds
Model Performance:
- Correlation: 0.00261
- Root Mean Squared Error: 1370.52 mm
- Peak Error: 4794.290 %
9	0.00263346	0.00331085	0.00096	184.16
Prediction time: 11.48 seconds
Model Performance:
- Correlation: -0.14550
- Root Mean Squared Error: 5797.54 mm
- Peak Error: 15571.246 %
10	0.00384201	0.00374574	0.00095	205.18
11	0.00203115	0.00192797	0.00095	214.16
Prediction time: 11.38 seconds
Model Performance:
- Correlation: -0.00281
- Root Mean Squared Error: 109.51 mm
- Peak Error: 52.956 %
12	0.00187151	0.00193433	0.00094	235.09
13	0.00313664	0.00306413	0.00094	244.17
14	0.00108915	0.00120720	0.00093	253.41
Prediction time: 12.64 seconds
Model Performance:
- Correlation: 0.09176
- Root Mean Squared Error: 2057.18 mm
- Peak Error: 8355.551 %
15	0.00166282	0.00129171	0.00093	275.76
16	0.00154999	0.00164851	0.00093	284.76
17	0.00109416	0.00095999	0.00092	293.73
Prediction time: 12.03 seconds
Model Performance:
- Correlation: 0.13545
- Root Mean Squared Error: 10122.31 mm
- Peak Error: 26934.365 %
18	0.00093621	0.00116940	0.00092	315.34
19	0.00074624	0.00070470	0.00091	324.31
Prediction time: 11.54 seconds
Model Performance:
- Correlation: 0.15631
- Root Mean Squared Error: 22392.78 mm
- Peak Error: 49396.041 %
20	0.00129790	0.00113335	0.00091	345.46
21	0.00140522	0.00136476	0.00090	354.38
22	0.00072352	0.00068254	0.00090	363.36
Prediction time: 12.34 seconds
Model Performance:
- Correlation: -0.15707
- Root Mean Squared Error: 39560.79 mm
- Peak Error: 81010.116 %
23	0.00055063	0.00070235	0.00090	385.28
24	0.00063199	0.00054079	0.00089	394.17
Prediction time: 11.75 seconds
Model Performance:
- Correlation: -0.15339
- Root Mean Squared Error: 44803.13 mm
- Peak Error: 86898.350 %
25	0.00058663	0.00047305	0.00089	415.40
Prediction time: 11.56 seconds
Model Performance:
- Correlation: 0.15742
- Root Mean Squared Error: 33274.10 mm
- Peak Error: 69389.634 %
26	0.00073351	0.00068156	0.00088	436.53
27	0.00070008	0.00062213	0.00088	446.02
28	0.00058901	0.00054832	0.00088	455.59
29	0.00043945	0.00053158	0.00087	465.10
30	0.00078992	0.00074469	0.00087	474.73
31	0.00070566	0.00065155	0.00087	484.98
32	0.00037713	0.00049332	0.00086	494.80
33	0.00091318	0.00097204	0.00086	504.18
34	0.00059339	0.00064612	0.00085	513.76
35	0.00070853	0.00087309	0.00085	524.01
36	0.00054572	0.00057476	0.00085	533.88
37	0.00044946	0.00050615	0.00084	543.43
38	0.00042737	0.00048759	0.00084	552.81
39	0.00078114	0.00053446	0.00084	561.99
40	0.00064655	0.00075780	0.00083	571.08
41	0.00041957	0.00048211	0.00083	580.07
42	0.00097785	0.00058222	0.00083	589.07
43	0.00060603	0.00050104	0.00082	598.16
44	0.00096750	0.00072221	0.00082	607.23
45	0.00055012	0.00043411	0.00082	616.34
Prediction time: 12.98 seconds
Model Performance:
- Correlation: 0.15122
- Root Mean Squared Error: 39832.14 mm
- Peak Error: 75510.643 %
46	0.00052105	0.00053993	0.00081	639.20
47	0.00042489	0.00043024	0.00081	649.05
Prediction time: 13.18 seconds
Model Performance:
- Correlation: 0.15164
- Root Mean Squared Error: 38360.26 mm
- Peak Error: 73063.753 %
48	0.00076671	0.00059203	0.00081	671.79
49	0.00056613	0.00052348	0.00080	681.08
50	0.00053436	0.00046583	0.00080	690.37
51	0.00053571	0.00051602	0.00080	699.84
52	0.00036794	0.00042057	0.00079	709.82
Prediction time: 14.21 seconds
Model Performance:
- Correlation: -0.15309
- Root Mean Squared Error: 32961.40 mm
- Peak Error: 63799.738 %
53	0.00050687	0.00039930	0.00079	734.93
Prediction time: 14.44 seconds
Model Performance:
- Correlation: -0.15441
- Root Mean Squared Error: 30913.28 mm
- Peak Error: 60644.128 %
54	0.00042083	0.00044588	0.00079	759.72
55	0.00056174	0.00050582	0.00078	769.19
56	0.00052942	0.00060419	0.00078	779.12
57	0.00046767	0.00043911	0.00078	788.85
58	0.00086036	0.00043932	0.00078	798.66
59	0.00042763	0.00048517	0.00077	808.53
60	0.00041497	0.00045261	0.00077	818.24
61	0.00045118	0.00043407	0.00077	827.74
62	0.00039926	0.00044771	0.00076	837.46
63	0.00048416	0.00056868	0.00076	847.13
64	0.00050393	0.00055117	0.00076	857.12
65	0.00039891	0.00038193	0.00075	867.00
Prediction time: 13.90 seconds
Model Performance:
- Correlation: 0.15123
- Root Mean Squared Error: 35800.64 mm
- Peak Error: 67751.233 %
