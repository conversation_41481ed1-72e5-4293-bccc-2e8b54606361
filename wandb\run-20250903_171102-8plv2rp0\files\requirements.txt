absl-py==2.1.0
accelerate==0.34.2
alembic==1.14.1
annotated-types==0.7.0
asttokens==2.4.1
audioread==3.0.1
backcall==0.2.0
basemap==1.4.1
basemap-data==1.3.2
Bottleneck==1.3.5
Brotli==1.0.9
certifi==2024.6.2
cffi==1.16.0
charset-normalizer==2.0.4
click==8.1.7
cmaes==0.11.1
colorama==0.4.6
colorlog==6.9.0
comm==0.2.1
contourpy==1.2.0
cryptography==41.0.7
cycler==0.11.0
debugpy==1.6.7
decorator==5.1.1
diffusers==0.30.2
docker-pycreds==0.4.0
et-xmlfile==1.1.0
eval_type_backport==0.2.2
executing==2.0.1
filelock==3.13.1
fonttools==4.25.0
fsspec==2024.9.0
future==1.0.0
gitdb==4.0.11
GitPython==3.1.43
gmpy2==2.1.2
greenlet==3.0.1
grpcio==1.48.2
huggingface-hub==0.24.7
idna==3.4
imageio==2.34.1
imageio-ffmpeg==0.5.1
importlib-metadata==7.0.1
importlib-resources==6.1.1
ipykernel==6.29.0
ipython==8.12.0
jedi==0.19.1
Jinja2==3.1.2
joblib==1.3.2
jupyter_client==8.6.0
jupyter_core==4.12.0
kiwisolver==1.4.4
lazy_loader==0.3
librosa==0.10.1
llvmlite==0.42.0
lxml==4.9.3
Mako==1.3.8
Markdown==3.6
MarkupSafe==2.1.3
matplotlib==3.8.0
matplotlib-inline==0.1.6
mkl-fft==1.3.8
mkl-random==1.2.4
mkl-service==2.4.0
mpmath==1.3.0
msgpack==1.0.7
munkres==1.1.4
nest_asyncio==1.6.0
networkx==3.1
numba==0.59.0
numexpr==2.8.7
numpy==1.26.3
opencv-python==*********
openpyxl==3.1.4
optuna==4.2.0
packaging==23.2
pandas==2.1.4
parso==0.8.3
pickleshare==0.7.5
Pillow==10.0.1
pip==23.3.1
platformdirs==4.2.0
ply==3.11
pooch==1.8.0
prompt-toolkit==3.0.42
protobuf==3.20.3
psutil==5.9.0
psycopg2==2.9.10
psycopg2-binary==2.9.10
pure-eval==0.2.2
pycparser==2.21
pydantic==2.10.4
pydantic_core==2.27.2
Pygments==2.17.2
PyKrige==1.7.1
pynndescent==0.5.13
pyOpenSSL==23.2.0
pyparsing==3.0.9
pyproj==3.6.1
PyQt5==5.15.10
PyQt5-sip==12.13.0
pyshp==2.3.1
PySocks==1.7.1
python-dateutil==2.8.2
pytz==2023.4
pywin32==227
PyYAML==6.0.1
pyzmq==25.1.2
regex==2024.9.11
requests==2.31.0
safetensors==0.4.5
scikit-learn==1.6.1
scipy==1.12.0
sentry-sdk==2.19.2
setproctitle==1.3.4
setuptools==68.2.2
sip==6.7.12
six==1.16.0
smmap==5.0.1
soundfile==0.12.1
soxr==0.3.7
SQLAlchemy==1.4.51
stack-data==0.6.2
sympy==1.12
tensorboard==2.16.2
tensorboard-data-server==0.7.0
threadpoolctl==3.2.0
tokenizers==0.19.1
tomli==2.0.1
torch==2.2.0
torchaudio==2.2.0
torchdiffeq==0.2.5
torchinfo==1.8.0
torchvision==0.17.0
tornado==6.2
tqdm==4.65.0
traitlets==5.14.1
transformers==4.44.2
typing_extensions==4.12.2
tzdata==2023.4
umap-learn==0.5.9.post2
urllib3==1.26.18
wandb==0.21.0
wcwidth==0.2.13
Werkzeug==3.0.2
wheel==0.41.2
win-inet-pton==1.1.0
xlrd==2.0.1
zipp==3.17.0
