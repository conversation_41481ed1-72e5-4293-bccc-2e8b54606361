Shape of input  per step: torch.Size([64, 13])
Shape of output per step: torch.Size([64, 6])
Dataset length: 438300
Shape of input  per step: torch.Size([64, 13])
Shape of output per step: torch.Size([64, 6])
Dataset length: 401490
TrainingLSTM(
  (head): GRU(12, 256, batch_first=True)
  (hidden): ModuleList(
    (0-2): 3 x GRU(256, 256, batch_first=True)
  )
  (fc): Sequential(
    (0): Linear(in_features=256, out_features=256, bias=True)
    (1): Tanh()
    (2): Linear(in_features=256, out_features=6, bias=True)
  )
)
1458950 paramerters in total
0	0.25850487	0.25045753	0.00100	3.22
Prediction time: 13.12 seconds
Model Performance:
- Correlation: -0.00158
- Root Mean Squared Error: 954.22 mm
- Peak Error: 47149.552 %
D:\anaconda3\envs\torch22\lib\site-packages\torch\optim\lr_scheduler.py:143: UserWarning: Detected call of `lr_scheduler.step()` before `optimizer.step()`. In PyTorch 1.1.0 and later, you should call them in the opposite order: `optimizer.step()` before `lr_scheduler.step()`.  Failure to do this will result in PyTorch skipping the first value of the learning rate schedule. See more details at https://pytorch.org/docs/stable/optim.html#how-to-adjust-learning-rate
  warnings.warn("Detected call of `lr_scheduler.step()` before `optimizer.step()`. "
1	0.00484237	0.00505772	0.00100	24.50
Prediction time: 14.35 seconds
Model Performance:
- Correlation: 0.00561
- Root Mean Squared Error: 145.71 mm
- Peak Error: 11974.456 %
2	0.00330011	0.00360002	0.00099	47.25
Prediction time: 13.25 seconds
Model Performance:
- Correlation: -0.00612
- Root Mean Squared Error: 223.81 mm
- Peak Error: 15708.152 %
3	0.00187883	0.00207858	0.00099	68.87
Prediction time: 13.54 seconds
Model Performance:
- Correlation: 0.03689
- Root Mean Squared Error: 107.48 mm
- Peak Error: 6654.750 %
4	0.00232866	0.00213791	0.00098	90.95
5	0.00128089	0.00176305	0.00098	98.57
Prediction time: 13.13 seconds
Model Performance:
- Correlation: 0.02773
- Root Mean Squared Error: 76.60 mm
- Peak Error: 5988.473 %
6	0.00171299	0.00172865	0.00097	120.06
Prediction time: 14.14 seconds
Model Performance:
- Correlation: 0.06712
- Root Mean Squared Error: 48.78 mm
- Peak Error: 2283.323 %
7	0.00122298	0.00164720	0.00097	142.49
Prediction time: 13.63 seconds
Model Performance:
- Correlation: 0.05997
- Root Mean Squared Error: 39.25 mm
- Peak Error: 1967.990 %
8	0.00163180	0.00174171	0.00096	164.57
9	0.00104599	0.00151535	0.00096	172.50
Prediction time: 14.21 seconds
Model Performance:
- Correlation: 0.02968
- Root Mean Squared Error: 56.62 mm
- Peak Error: 3954.862 %
10	0.00167097	0.00123714	0.00095	195.49
Prediction time: 14.34 seconds
Model Performance:
- Correlation: 0.04526
- Root Mean Squared Error: 49.67 mm
- Peak Error: 3118.421 %
11	0.00124441	0.00135074	0.00095	217.56
12	0.00108099	0.00113976	0.00094	224.71
Prediction time: 12.48 seconds
Model Performance:
- Correlation: 0.07511
- Root Mean Squared Error: 53.84 mm
- Peak Error: 3317.971 %
