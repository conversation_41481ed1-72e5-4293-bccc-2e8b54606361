Shape of input  per step: torch.Size([90, 13])
Shape of output per step: torch.Size([90, 6])
Dataset length: 425430
Shape of input  per step: torch.Size([90, 13])
Shape of output per step: torch.Size([90, 6])
Dataset length: 397629
TrainingLSTM(
  (head): GRU(12, 256, batch_first=True)
  (hidden): ModuleList(
    (0-2): 3 x GRU(256, 256, batch_first=True)
  )
  (fc): Sequential(
    (0): Linear(in_features=256, out_features=256, bias=True)
    (1): Tanh()
    (2): Linear(in_features=256, out_features=6, bias=True)
  )
)
1458950 paramerters in total
0	0.36314249	0.36621448	0.00100	3.61
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 13.63 seconds
Model Performance:
- Correlation: 0.00495
- Root Mean Squared Error: 1193.78 mm
- Peak Error: 42270.432 %
D:\anaconda3\envs\torch22\lib\site-packages\torch\optim\lr_scheduler.py:143: UserWarning: Detected call of `lr_scheduler.step()` before `optimizer.step()`. In PyTorch 1.1.0 and later, you should call them in the opposite order: `optimizer.step()` before `lr_scheduler.step()`.  Failure to do this will result in PyTorch skipping the first value of the learning rate schedule. See more details at https://pytorch.org/docs/stable/optim.html#how-to-adjust-learning-rate
  warnings.warn("Detected call of `lr_scheduler.step()` before `optimizer.step()`. "
1	0.00453146	0.00538560	0.00100	27.04
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 15.45 seconds
Model Performance:
- Correlation: -0.00572
- Root Mean Squared Error: 71.11 mm
- Peak Error: 4940.091 %
2	0.00416308	0.00487887	0.00099	53.03
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 15.96 seconds
Model Performance:
- Correlation: -0.00259
- Root Mean Squared Error: 332.64 mm
- Peak Error: 17472.784 %
3	0.00170739	0.00170099	0.00099	79.37
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 15.12 seconds
Model Performance:
- Correlation: 0.01541
- Root Mean Squared Error: 305.04 mm
- Peak Error: 15754.444 %
4	0.00177111	0.00160565	0.00098	105.13
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 14.80 seconds
Model Performance:
- Correlation: 0.03205
- Root Mean Squared Error: 90.89 mm
- Peak Error: 4367.900 %
5	0.00127341	0.00129336	0.00098	129.76
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 15.06 seconds
Model Performance:
- Correlation: 0.03736
- Root Mean Squared Error: 71.40 mm
- Peak Error: 4046.178 %
6	0.00136121	0.00152836	0.00097	154.76
7	0.00254881	0.00171495	0.00097	164.76
8	0.00160818	0.00143589	0.00096	174.11
9	0.00127968	0.00130780	0.00096	183.24
10	0.00124347	0.00102020	0.00095	192.51
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 13.89 seconds
Model Performance:
- Correlation: 0.19463
- Root Mean Squared Error: 17.56 mm
- Peak Error: 611.354 %
11	0.00109279	0.00141525	0.00095	216.47
12	0.00064484	0.00088310	0.00094	225.89
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 13.77 seconds
Model Performance:
- Correlation: 0.25794
- Root Mean Squared Error: 6.49 mm
- Peak Error: 224.572 %
13	0.00065526	0.00073706	0.00094	249.71
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 14.71 seconds
Model Performance:
- Correlation: 0.19501
- Root Mean Squared Error: 23.79 mm
- Peak Error: 985.966 %
14	0.00055919	0.00062721	0.00093	274.45
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 13.71 seconds
Model Performance:
- Correlation: 0.23954
- Root Mean Squared Error: 11.44 mm
- Peak Error: 383.565 %
15	0.00072710	0.00066059	0.00093	298.50
16	0.00056210	0.00054089	0.00093	307.59
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 13.64 seconds
Model Performance:
- Correlation: 0.48398
- Root Mean Squared Error: 2.60 mm
- Peak Error: 44.430 %
17	0.00069111	0.00062492	0.00092	331.28
18	0.00063807	0.00055861	0.00092	340.11
19	0.00059553	0.00058032	0.00091	349.10
20	0.00055398	0.00054454	0.00091	358.44
21	0.00072880	0.00052361	0.00090	367.54
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 14.68 seconds
Model Performance:
- Correlation: 0.23158
- Root Mean Squared Error: 12.13 mm
- Peak Error: 416.224 %
22	0.00059812	0.00056468	0.00090	392.25
23	0.00045617	0.00050195	0.00090	401.23
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 14.80 seconds
Model Performance:
- Correlation: 0.33525
- Root Mean Squared Error: 8.75 mm
- Peak Error: 279.355 %
24	0.00050661	0.00052772	0.00089	426.11
25	0.00048384	0.00047470	0.00089	435.49
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 12.90 seconds
Model Performance:
- Correlation: 0.33371
- Root Mean Squared Error: 12.32 mm
- Peak Error: 325.663 %
26	0.00047652	0.00041138	0.00088	457.48
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 11.93 seconds
Model Performance:
- Correlation: 0.16525
- Root Mean Squared Error: 18.92 mm
- Peak Error: 626.221 %
27	0.00034221	0.00040549	0.00088	478.64
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 12.00 seconds
Model Performance:
- Correlation: 0.18522
- Root Mean Squared Error: 41.07 mm
- Peak Error: 1634.999 %
28	0.00036577	0.00038359	0.00088	499.58
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 12.52 seconds
Model Performance:
- Correlation: 0.35630
- Root Mean Squared Error: 9.36 mm
- Peak Error: 412.776 %
29	0.00033431	0.00033419	0.00087	521.29
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 12.22 seconds
Model Performance:
- Correlation: 0.39783
- Root Mean Squared Error: 4.02 mm
- Peak Error: 91.123 %
30	0.00051133	0.00035227	0.00087	542.77
31	0.00030555	0.00033777	0.00087	551.62
32	0.00029658	0.00032692	0.00086	560.81
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 12.64 seconds
Model Performance:
- Correlation: 0.32641
- Root Mean Squared Error: 7.14 mm
- Peak Error: 207.203 %
33	0.00024496	0.00027549	0.00086	582.56
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 12.60 seconds
Model Performance:
- Correlation: 0.68773
- Root Mean Squared Error: 1.80 mm
- Peak Error: 24.608 %
34	0.00035977	0.00027251	0.00085	604.27
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 12.58 seconds
Model Performance:
- Correlation: 0.29506
- Root Mean Squared Error: 10.52 mm
- Peak Error: 283.967 %
35	0.00024114	0.00031682	0.00085	626.20
36	0.00040496	0.00027713	0.00085	634.54
37	0.00023741	0.00029757	0.00084	642.94
38	0.00023512	0.00031716	0.00084	651.32
39	0.00026576	0.00028139	0.00084	659.75
40	0.00023561	0.00027461	0.00083	668.12
41	0.00020959	0.00026277	0.00083	676.54
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 12.95 seconds
Model Performance:
- Correlation: 0.30954
- Root Mean Squared Error: 8.87 mm
- Peak Error: 323.511 %
42	0.00024959	0.00026326	0.00083	698.77
43	0.00034343	0.00032443	0.00082	707.29
44	0.00032490	0.00036695	0.00082	715.73
45	0.00029337	0.00030131	0.00082	724.12
46	0.00027483	0.00025968	0.00081	732.44
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 12.99 seconds
Model Performance:
- Correlation: 0.25035
- Root Mean Squared Error: 11.95 mm
- Peak Error: 351.986 %
47	0.00024445	0.00023751	0.00081	754.38
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 12.85 seconds
Model Performance:
- Correlation: 0.56186
- Root Mean Squared Error: 2.99 mm
- Peak Error: 50.607 %
48	0.00028665	0.00026215	0.00081	776.29
49	0.00026485	0.00025034	0.00080	784.57
50	0.00023427	0.00025334	0.00080	792.95
51	0.00019801	0.00026610	0.00080	801.23
52	0.00024584	0.00024594	0.00079	809.53
53	0.00026409	0.00023131	0.00079	817.78
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 12.32 seconds
Model Performance:
- Correlation: 0.38315
- Root Mean Squared Error: 4.74 mm
- Peak Error: 127.408 %
54	0.00019699	0.00027943	0.00079	839.15
55	0.00036002	0.00026282	0.00078	847.41
56	0.00020413	0.00024954	0.00078	855.71
57	0.00027788	0.00028673	0.00078	863.96
58	0.00024833	0.00026755	0.00078	872.23
59	0.00025786	0.00024000	0.00077	880.57
60	0.00024930	0.00024157	0.00077	888.94
61	0.00029099	0.00025216	0.00077	897.20
62	0.00022081	0.00023612	0.00076	905.47
63	0.00021116	0.00023055	0.00076	913.78
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 11.90 seconds
Model Performance:
- Correlation: 0.65159
- Root Mean Squared Error: 2.21 mm
- Peak Error: 53.947 %
64	0.00018489	0.00020944	0.00076	934.72
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 12.22 seconds
Model Performance:
- Correlation: 0.75753
- Root Mean Squared Error: 1.44 mm
- Peak Error: 17.985 %
65	0.00026263	0.00023226	0.00075	956.04
66	0.00020155	0.00022435	0.00075	964.37
67	0.00024841	0.00022257	0.00075	972.58
68	0.00019534	0.00022538	0.00075	980.86
69	0.00018949	0.00022623	0.00074	989.21
70	0.00018946	0.00020930	0.00074	997.59
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 12.25 seconds
Model Performance:
- Correlation: 0.32454
- Root Mean Squared Error: 7.19 mm
- Peak Error: 225.803 %
71	0.00030780	0.00023315	0.00074	1018.84
72	0.00023320	0.00021132	0.00074	1027.02
73	0.00020556	0.00022603	0.00073	1035.28
74	0.00023223	0.00022233	0.00073	1043.58
75	0.00020379	0.00021681	0.00073	1051.89
76	0.00022685	0.00020980	0.00072	1060.13
77	0.00017073	0.00022517	0.00072	1068.45
78	0.00016932	0.00021148	0.00072	1076.73
79	0.00018125	0.00022007	0.00072	1085.02
80	0.00021793	0.00020842	0.00071	1093.24
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 12.51 seconds
Model Performance:
- Correlation: 0.40483
- Root Mean Squared Error: 4.46 mm
- Peak Error: 113.051 %
81	0.00025076	0.00019819	0.00071	1114.82
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 12.76 seconds
Model Performance:
- Correlation: 0.44975
- Root Mean Squared Error: 3.51 mm
- Peak Error: 76.604 %
82	0.00018595	0.00022201	0.00071	1136.48
83	0.00028050	0.00021194	0.00071	1144.83
84	0.00016431	0.00020387	0.00070	1153.26
85	0.00023114	0.00023842	0.00070	1161.61
86	0.00023378	0.00020629	0.00070	1170.03
87	0.00020511	0.00020365	0.00070	1178.37
88	0.00019869	0.00020550	0.00069	1186.77
89	0.00017933	0.00020698	0.00069	1195.43
90	0.00024416	0.00022157	0.00069	1203.89
91	0.00019652	0.00019411	0.00069	1212.25
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 12.78 seconds
Model Performance:
- Correlation: 0.36746
- Root Mean Squared Error: 5.45 mm
- Peak Error: 146.474 %
92	0.00017713	0.00018186	0.00068	1234.03
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 12.72 seconds
Model Performance:
- Correlation: 0.26627
- Root Mean Squared Error: 12.64 mm
- Peak Error: 479.861 %
93	0.00015839	0.00019516	0.00068	1255.75
94	0.00017188	0.00018302	0.00068	1264.22
95	0.00017454	0.00018313	0.00068	1272.60
96	0.00014303	0.00018311	0.00068	1281.49
97	0.00018751	0.00018319	0.00067	1291.69
98	0.00021540	0.00019358	0.00067	1301.29
99	0.00016446	0.00017973	0.00067	1311.28
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 15.01 seconds
Model Performance:
- Correlation: 0.12368
- Root Mean Squared Error: 37.23 mm
- Peak Error: 1312.422 %
100	0.00017023	0.00016926	0.00067	1335.36
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 12.38 seconds
Model Performance:
- Correlation: 0.12241
- Root Mean Squared Error: 34.55 mm
- Peak Error: 1355.082 %
