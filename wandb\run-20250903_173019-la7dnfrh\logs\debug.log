2025-09-03 17:30:19,597 INFO    MainThread:24420 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-09-03 17:30:19,597 INFO    MainThread:24420 [wandb_setup.py:_flush():80] Configure stats pid to 24420
2025-09-03 17:30:19,597 INFO    MainThread:24420 [wandb_setup.py:_flush():80] Loading settings from C:\Users\<USER>\.config\wandb\settings
2025-09-03 17:30:19,597 INFO    MainThread:24420 [wandb_setup.py:_flush():80] Loading settings from E:\Codes\recLSTM-bridge\wandb\settings
2025-09-03 17:30:19,597 INFO    MainThread:24420 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-09-03 17:30:19,597 INFO    MainThread:24420 [wandb_init.py:setup_run_log_directory():703] Logging user logs to E:\Codes\recLSTM-bridge\wandb\run-20250903_173019-la7dnfrh\logs\debug.log
2025-09-03 17:30:19,598 INFO    MainThread:24420 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to E:\Codes\recLSTM-bridge\wandb\run-20250903_173019-la7dnfrh\logs\debug-internal.log
2025-09-03 17:30:19,598 INFO    MainThread:24420 [wandb_init.py:init():830] calling init triggers
2025-09-03 17:30:19,598 INFO    MainThread:24420 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'bridge_idx': 33, 'response': 'bear', 'load_path': 'data/', 'save_path': 'save/', 'pred_dim': 1, 'dt': 0.02, 'zero_init': False, 'shift_acc': False, 'flip': False, 'fliplr': True, 'auto_slice': False, 'past_history': 120, 'local_sample_step': 3, 'global_sample_step': 1, 'window_sliding_step': 1, 'layers': 4, 'width': 256, 'split': 0.95, 'epochs': 2000, 'initial_epoch': 0, 'batch_size': 512, 'initial_lr': 0.001, 'decay': 0.005, 'resume': 'none', 'loss_length': 40, 'use_amp': True, 'adan': True, 'max_grad_norm': 1.0, 'weight_decay': 0.01, 'opt_eps': 1e-08, 'opt_betas': None, 'no_prox': False, '_wandb': {'code_path': 'code/train.py'}}
2025-09-03 17:30:19,598 INFO    MainThread:24420 [wandb_init.py:init():871] starting backend
2025-09-03 17:30:19,983 INFO    MainThread:24420 [wandb_init.py:init():874] sending inform_init request
2025-09-03 17:30:20,003 INFO    MainThread:24420 [wandb_init.py:init():882] backend started and connected
2025-09-03 17:30:20,004 INFO    MainThread:24420 [wandb_init.py:init():953] updated telemetry
2025-09-03 17:30:20,005 INFO    MainThread:24420 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-09-03 17:30:24,136 INFO    MainThread:24420 [wandb_init.py:init():1029] starting run threads in backend
2025-09-03 17:30:24,222 INFO    MainThread:24420 [wandb_run.py:_console_start():2458] atexit reg
2025-09-03 17:30:24,222 INFO    MainThread:24420 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-09-03 17:30:24,223 INFO    MainThread:24420 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-09-03 17:30:24,223 INFO    MainThread:24420 [wandb_run.py:_redirect():2398] Redirects installed.
2025-09-03 17:30:24,224 INFO    MainThread:24420 [wandb_init.py:init():1075] run started, returning control to user process
2025-09-03 23:21:09,362 INFO    MainThread:24420 [wandb_run.py:_finish():2224] finishing run xzk8559/recLSTM-bridge/la7dnfrh
2025-09-03 23:21:09,363 INFO    MainThread:24420 [wandb_run.py:_atexit_cleanup():2423] got exitcode: 0
2025-09-03 23:21:09,363 INFO    MainThread:24420 [wandb_run.py:_restore():2405] restore
2025-09-03 23:21:09,364 INFO    MainThread:24420 [wandb_run.py:_restore():2411] restore done
2025-09-03 23:21:14,913 INFO    MainThread:24420 [wandb_run.py:_footer_history_summary_info():3903] rendering history
2025-09-03 23:21:14,914 INFO    MainThread:24420 [wandb_run.py:_footer_history_summary_info():3935] rendering summary
2025-09-03 23:21:14,914 INFO    MainThread:24420 [wandb_run.py:_footer_sync_info():3864] logging synced files
