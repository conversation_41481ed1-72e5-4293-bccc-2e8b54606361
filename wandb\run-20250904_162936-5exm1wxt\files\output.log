Shape of input  per step: torch.Size([120, 5])
Shape of output per step: torch.Size([120, 2])
Dataset length: 417330
Shape of input  per step: torch.Size([120, 5])
Shape of output per step: torch.Size([120, 2])
Dataset length: 395199
TrainingLSTM(
  (head): GRU(4, 256, batch_first=True)
  (hidden): ModuleList(
    (0-2): 3 x GRU(256, 256, batch_first=True)
  )
  (fc): Sequential(
    (0): Linear(in_features=256, out_features=256, bias=True)
    (1): Tanh()
    (2): Linear(in_features=256, out_features=2, bias=True)
  )
)
1451778 paramerters in total
0	0.64305395	0.64200777	0.00100	4.00
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 16.61 seconds
Model Performance:
- Correlation: -0.06841
- Root Mean Squared Error: 1.65 mm
- Peak Error: 96.552 %
D:\anaconda3\envs\torch22\lib\site-packages\torch\optim\lr_scheduler.py:143: UserWarning: Detected call of `lr_scheduler.step()` before `optimizer.step()`. In PyTorch 1.1.0 and later, you should call them in the opposite order: `optimizer.step()` before `lr_scheduler.step()`.  Failure to do this will result in PyTorch skipping the first value of the learning rate schedule. See more details at https://pytorch.org/docs/stable/optim.html#how-to-adjust-learning-rate
  warnings.warn("Detected call of `lr_scheduler.step()` before `optimizer.step()`. "
1	0.00535831	0.00534098	0.00100	30.97
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 16.81 seconds
Model Performance:
- Correlation: 0.44220
- Root Mean Squared Error: 1.59 mm
- Peak Error: 86.225 %
2	0.00139952	0.00164743	0.00099	58.53
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 20.04 seconds
Model Performance:
- Correlation: 0.13732
- Root Mean Squared Error: 14.03 mm
- Peak Error: 457.637 %
3	0.00124857	0.00112619	0.00099	89.87
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 19.78 seconds
Model Performance:
- Correlation: 0.62920
- Root Mean Squared Error: 2.11 mm
- Peak Error: 47.081 %
4	0.00114311	0.00132183	0.00098	120.60
5	0.00067028	0.00100774	0.00098	131.28
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 19.33 seconds
Model Performance:
- Correlation: 0.08712
- Root Mean Squared Error: 16.84 mm
- Peak Error: 540.961 %
6	0.00101515	0.00107091	0.00097	162.06
7	0.00110385	0.00104517	0.00097	172.49
8	0.00118454	0.00081567	0.00096	182.83
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 20.54 seconds
Model Performance:
- Correlation: 0.86842
- Root Mean Squared Error: 1.11 mm
- Peak Error: 15.788 %
9	0.00090892	0.00080357	0.00096	214.54
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 18.95 seconds
Model Performance:
- Correlation: 0.29325
- Root Mean Squared Error: 6.74 mm
- Peak Error: 173.822 %
10	0.00075264	0.00073776	0.00095	244.70
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 18.89 seconds
Model Performance:
- Correlation: 0.70304
- Root Mean Squared Error: 2.09 mm
- Peak Error: 41.890 %
11	0.00086952	0.00070500	0.00095	274.47
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 20.93 seconds
Model Performance:
- Correlation: 0.73203
- Root Mean Squared Error: 2.02 mm
- Peak Error: 19.191 %
12	0.00056322	0.00051830	0.00094	306.72
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 21.29 seconds
Model Performance:
- Correlation: 0.24712
- Root Mean Squared Error: 7.99 mm
- Peak Error: 254.542 %
13	0.00033861	0.00046349	0.00094	339.27
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 20.30 seconds
Model Performance:
- Correlation: 0.91940
- Root Mean Squared Error: 0.97 mm
- Peak Error: 12.388 %
14	0.00039908	0.00036538	0.00093	370.56
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 19.78 seconds
Model Performance:
- Correlation: 0.92816
- Root Mean Squared Error: 0.97 mm
- Peak Error: 12.304 %
15	0.00061592	0.00049312	0.00093	401.88
16	0.00032213	0.00033909	0.00093	412.19
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 19.73 seconds
Model Performance:
- Correlation: 0.63631
- Root Mean Squared Error: 2.96 mm
- Peak Error: 50.452 %
17	0.00035673	0.00036813	0.00092	443.34
18	0.00047517	0.00037994	0.00092	453.39
19	0.00026038	0.00031719	0.00091	463.60
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 19.90 seconds
Model Performance:
- Correlation: 0.79535
- Root Mean Squared Error: 1.64 mm
- Peak Error: 16.116 %
20	0.00034733	0.00032786	0.00091	494.51
21	0.00025841	0.00030609	0.00090	504.76
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 20.19 seconds
Model Performance:
- Correlation: 0.89624
- Root Mean Squared Error: 1.07 mm
- Peak Error: 11.838 %
22	0.00034730	0.00031113	0.00090	536.19
23	0.00026169	0.00030256	0.00090	546.57
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 21.15 seconds
Model Performance:
- Correlation: 0.94105
- Root Mean Squared Error: 0.88 mm
- Peak Error: 10.446 %
24	0.00031116	0.00033983	0.00089	579.01
25	0.00053053	0.00053699	0.00089	589.47
26	0.00022618	0.00026523	0.00088	599.88
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 20.62 seconds
Model Performance:
- Correlation: 0.92034
- Root Mean Squared Error: 0.94 mm
- Peak Error: 10.119 %
27	0.00023283	0.00024733	0.00088	631.75
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 20.51 seconds
Model Performance:
- Correlation: 0.93441
- Root Mean Squared Error: 0.94 mm
- Peak Error: 8.777 %
28	0.00016555	0.00020600	0.00088	663.35
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 21.52 seconds
Model Performance:
- Correlation: 0.93157
- Root Mean Squared Error: 0.85 mm
- Peak Error: 9.461 %
29	0.00019260	0.00014854	0.00087	696.56
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 19.63 seconds
Model Performance:
- Correlation: 0.93720
- Root Mean Squared Error: 0.85 mm
- Peak Error: 10.321 %
30	0.00010409	0.00017408	0.00087	727.67
31	0.00014770	0.00018706	0.00087	738.23
32	0.00058062	0.00013052	0.00086	748.51
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 19.83 seconds
Model Performance:
- Correlation: 0.96323
- Root Mean Squared Error: 0.76 mm
- Peak Error: 7.670 %
33	0.00005710	0.00007806	0.00086	779.50
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 19.80 seconds
Model Performance:
- Correlation: 0.97141
- Root Mean Squared Error: 0.71 mm
- Peak Error: 7.570 %
34	0.00006697	0.00014002	0.00085	810.34
35	0.00035551	0.00043261	0.00085	820.82
36	0.00005811	0.00008536	0.00085	831.41
37	0.00027321	0.00008275	0.00084	841.98
38	0.00004855	0.00007357	0.00084	852.33
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 19.42 seconds
Model Performance:
- Correlation: 0.95288
- Root Mean Squared Error: 0.93 mm
- Peak Error: 7.233 %
39	0.00014656	0.00015729	0.00084	883.38
40	0.00006672	0.00008667	0.00083	893.72
41	0.00006045	0.00007566	0.00083	904.25
42	0.00007606	0.00010959	0.00083	913.78
43	0.00003230	0.00006166	0.00082	923.35
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 17.89 seconds
Model Performance:
- Correlation: 0.95390
- Root Mean Squared Error: 0.79 mm
- Peak Error: 7.088 %
44	0.00020712	0.00020876	0.00082	951.22
45	0.00005673	0.00007447	0.00082	960.61
46	0.00003377	0.00005586	0.00081	970.10
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 17.00 seconds
Model Performance:
- Correlation: 0.95531
- Root Mean Squared Error: 0.80 mm
- Peak Error: 7.860 %
47	0.00007797	0.00009254	0.00081	997.11
48	0.00009561	0.00004626	0.00081	1006.51
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 17.67 seconds
Model Performance:
- Correlation: 0.90835
- Root Mean Squared Error: 0.92 mm
- Peak Error: 8.900 %
49	0.00008830	0.00005557	0.00080	1034.19
50	0.00012897	0.00014981	0.00080	1043.63
51	0.00007065	0.00009296	0.00080	1053.07
52	0.00006203	0.00009474	0.00079	1062.57
53	0.00008200	0.00005855	0.00079	1071.95
54	0.00004911	0.00006459	0.00079	1081.37
55	0.00003131	0.00005441	0.00078	1090.75
56	0.00013923	0.00011819	0.00078	1100.21
57	0.00003768	0.00005403	0.00078	1109.61
58	0.00002562	0.00003500	0.00078	1119.04
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 16.82 seconds
Model Performance:
- Correlation: 0.94236
- Root Mean Squared Error: 0.90 mm
- Peak Error: 8.189 %
59	0.00006631	0.00008094	0.00077	1146.10
60	0.00002378	0.00005428	0.00077	1155.58
61	0.00003223	0.00004877	0.00077	1164.99
62	0.00004036	0.00003955	0.00076	1174.32
63	0.00002098	0.00003139	0.00076	1183.78
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 17.23 seconds
Model Performance:
- Correlation: 0.93779
- Root Mean Squared Error: 0.91 mm
- Peak Error: 8.061 %
64	0.00002830	0.00003333	0.00076	1211.19
65	0.00007876	0.00008820	0.00075	1220.55
66	0.00002389	0.00003535	0.00075	1229.90
67	0.00002066	0.00002916	0.00075	1239.31
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 17.79 seconds
Model Performance:
- Correlation: 0.94405
- Root Mean Squared Error: 0.84 mm
- Peak Error: 9.374 %
68	0.00003957	0.00004027	0.00075	1267.12
69	0.00002142	0.00002541	0.00074	1276.53
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 17.36 seconds
Model Performance:
- Correlation: 0.90893
- Root Mean Squared Error: 0.97 mm
- Peak Error: 7.354 %
70	0.00006613	0.00006595	0.00074	1303.91
71	0.00004518	0.00003388	0.00074	1313.30
72	0.00004882	0.00004685	0.00074	1322.65
73	0.00001720	0.00002862	0.00073	1332.01
74	0.00003062	0.00004777	0.00073	1341.28
75	0.00026510	0.00006400	0.00073	1350.54
76	0.00005357	0.00006573	0.00072	1359.91
77	0.00002493	0.00002794	0.00072	1369.33
78	0.00004117	0.00002542	0.00072	1378.74
79	0.00002012	0.00002094	0.00072	1388.13
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 17.53 seconds
Model Performance:
- Correlation: 0.95453
- Root Mean Squared Error: 0.87 mm
- Peak Error: 7.704 %
80	0.00002883	0.00002664	0.00071	1415.74
81	0.00002098	0.00004101	0.00071	1425.41
82	0.00001496	0.00002241	0.00071	1434.75
83	0.00007569	0.00009193	0.00071	1444.20
84	0.00002532	0.00002369	0.00070	1453.63
85	0.00004306	0.00002282	0.00070	1463.03
86	0.00013821	0.00002385	0.00070	1472.52
87	0.00004181	0.00005644	0.00070	1481.86
88	0.00004223	0.00004280	0.00069	1491.33
89	0.00001870	0.00001894	0.00069	1500.71
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 17.04 seconds
Model Performance:
- Correlation: 0.96906
- Root Mean Squared Error: 0.76 mm
- Peak Error: 8.440 %
90	0.00004389	0.00003859	0.00069	1527.98
91	0.00005863	0.00006415	0.00069	1537.39
92	0.00001245	0.00001787	0.00068	1546.75
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 16.87 seconds
Model Performance:
- Correlation: 0.94717
- Root Mean Squared Error: 0.80 mm
- Peak Error: 7.323 %
93	0.00002138	0.00004197	0.00068	1573.58
94	0.00002877	0.00003569	0.00068	1583.11
95	0.00005273	0.00006527	0.00068	1592.58
96	0.00003946	0.00003474	0.00068	1601.98
97	0.00002053	0.00002565	0.00067	1611.46
98	0.00003335	0.00003199	0.00067	1620.82
99	0.00003184	0.00004307	0.00067	1630.27
100	0.00002874	0.00002230	0.00067	1639.62
101	0.00001879	0.00002077	0.00066	1649.03
102	0.00001758	0.00002882	0.00066	1658.53
103	0.00001368	0.00001757	0.00066	1667.95
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 16.56 seconds
Model Performance:
- Correlation: 0.94981
- Root Mean Squared Error: 0.84 mm
- Peak Error: 7.437 %
104	0.00001617	0.00002013	0.00066	1694.46
105	0.00001346	0.00001478	0.00066	1703.83
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 16.94 seconds
Model Performance:
- Correlation: 0.96343
- Root Mean Squared Error: 0.83 mm
- Peak Error: 7.663 %
106	0.00002856	0.00002891	0.00065	1730.83
107	0.00003483	0.00005147	0.00065	1740.25
108	0.00001619	0.00002868	0.00065	1749.59
109	0.00002279	0.00003357	0.00065	1759.00
110	0.00001171	0.00002248	0.00065	1768.55
111	0.00002407	0.00002281	0.00064	1778.38
112	0.00002414	0.00002335	0.00064	1787.75
113	0.00003724	0.00004038	0.00064	1797.10
114	0.00004957	0.00005516	0.00064	1806.46
115	0.00002190	0.00001984	0.00063	1815.78
116	0.00005131	0.00004619	0.00063	1825.28
117	0.00001169	0.00002513	0.00063	1834.68
118	0.00004661	0.00001754	0.00063	1844.82
119	0.00001217	0.00001337	0.00063	1855.09
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 16.78 seconds
Model Performance:
- Correlation: 0.90581
- Root Mean Squared Error: 0.97 mm
- Peak Error: 7.410 %
120	0.00001140	0.00001517	0.00063	1881.99
121	0.00001194	0.00001406	0.00062	1891.28
122	0.00000994	0.00001614	0.00062	1900.64
123	0.00001888	0.00002202	0.00062	1910.13
124	0.00004672	0.00004895	0.00062	1919.60
125	0.00001735	0.00002676	0.00062	1928.93
