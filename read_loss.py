import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

data_tra = pd.read_csv('wandb/run-20250227_162549-s3xd1wv2/loss-train.csv').values
data_val = pd.read_csv('wandb/run-20250227_162549-s3xd1wv2/loss-val.csv').values
data_tes = pd.read_csv('wandb/run-20250227_162549-s3xd1wv2/loss-test.csv').values

#%%
plt.figure(dpi=150)
plt.plot(data_tra[:, 0], data_tra[:, 1], linewidth=0.5)
plt.plot(data_val[:, 0], data_val[:, 1], linewidth=0.5)
plt.plot(data_tes[:, 0], data_tes[:, 1], linewidth=0.5)
plt.xlabel('Epochs')
plt.ylabel('Loss')
plt.yscale('log')
plt.legend(['train', 'val', 'test'])
plt.grid(True, which='both', linestyle='--', linewidth=0.5)
plt.ylim(top=1e-4)


# %%
