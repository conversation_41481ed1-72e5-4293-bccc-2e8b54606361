{"time":"2025-09-02T17:34:31.7382627+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-09-02T17:34:33.3425252+08:00","level":"INFO","msg":"stream: created new stream","id":"2h6wk7tc"}
{"time":"2025-09-02T17:34:33.3425252+08:00","level":"INFO","msg":"stream: started","id":"2h6wk7tc"}
{"time":"2025-09-02T17:34:33.3429058+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"2h6wk7tc"}
{"time":"2025-09-02T17:34:33.3429058+08:00","level":"INFO","msg":"sender: started","stream_id":"2h6wk7tc"}
{"time":"2025-09-02T17:34:33.3429058+08:00","level":"INFO","msg":"handler: started","stream_id":"2h6wk7tc"}
{"time":"2025-09-02T17:34:33.8720874+08:00","level":"ERROR","msg":"git repo not found","error":"repository does not exist"}
{"time":"2025-09-02T18:37:41.8947617+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-02T19:36:03.774789+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": unexpected EOF"}
{"time":"2025-09-02T19:47:30.8293328+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": unexpected EOF"}
{"time":"2025-09-02T21:10:21.2755744+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-02T21:13:00.1170578+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-02T21:26:44.9248356+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-02T21:34:00.4014119+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-02T22:11:15.9186733+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": unexpected EOF"}
{"time":"2025-09-02T22:14:29.386636+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-02T22:27:03.6049479+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": unexpected EOF"}
{"time":"2025-09-02T22:32:47.7198877+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-02T22:34:59.0473929+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-02T22:37:19.0014967+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": unexpected EOF"}
{"time":"2025-09-02T23:06:30.687376+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-02T23:12:29.5758467+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-02T23:17:44.586915+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-02T23:22:36.2755265+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-02T23:31:38.1271842+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": unexpected EOF"}
{"time":"2025-09-02T23:34:36.600246+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-02T23:51:30.1381037+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": unexpected EOF"}
{"time":"2025-09-03T01:16:14.5153395+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": unexpected EOF"}
{"time":"2025-09-03T01:18:41.0383035+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-03T01:18:59.3390074+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": unexpected EOF"}
{"time":"2025-09-03T01:19:25.8505809+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-03T01:21:06.883804+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": unexpected EOF"}
{"time":"2025-09-03T01:23:41.1031167+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": unexpected EOF"}
{"time":"2025-09-03T01:24:35.9675301+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": unexpected EOF"}
{"time":"2025-09-03T08:53:00.9658332+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": unexpected EOF"}
{"time":"2025-09-03T09:02:08.8725343+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-03T09:06:55.1959083+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-03T09:07:26.2399309+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-03T09:07:38.2701771+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-03T09:13:56.2012644+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-03T09:38:09.1911763+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-03T09:42:56.4874407+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": unexpected EOF"}
{"time":"2025-09-03T09:43:17.1762797+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": unexpected EOF"}
{"time":"2025-09-03T09:55:23.6670691+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-03T10:18:08.2563241+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-03T11:00:38.7518176+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-03T12:04:48.8631101+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": unexpected EOF"}
{"time":"2025-09-03T12:12:46.7025613+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-03T12:18:25.6438772+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-03T12:19:24.3747406+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-03T12:21:24.7026533+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-03T12:23:24.3657899+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-03T12:24:25.3684926+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-03T12:29:06.2887269+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-03T12:36:57.7903675+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-03T12:37:04.5294645+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-03T12:44:49.6484279+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": unexpected EOF"}
{"time":"2025-09-03T12:56:09.5315994+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-03T12:59:39.6522436+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-03T13:00:24.9541889+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-03T13:07:25.3785875+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
{"time":"2025-09-03T13:40:09.3631306+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/2h6wk7tc/file_stream\": EOF"}
