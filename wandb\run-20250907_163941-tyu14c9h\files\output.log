Shape of input  per step: torch.Size([120, 5])
Shape of output per step: torch.Size([120, 2])
Dataset length: 834660
Shape of input  per step: torch.Size([120, 5])
Shape of output per step: torch.Size([120, 2])
Dataset length: 395199
TrainingRNN(
  (head): SymmetrizedBlock(
    (rnn): GRU(4, 256, batch_first=True)
  )
  (hidden): ModuleList(
    (0-2): 3 x SymmetrizedBlock(
      (rnn): GRU(256, 256, batch_first=True)
    )
  )
  (fc): Sequential(
    (0): Linear(in_features=256, out_features=256, bias=False)
    (1): Tanh()
    (2): Linear(in_features=256, out_features=2, bias=False)
  )
)
1451520 paramerters in total
0	0.00032356	0.00043409	0.00100	7.15
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 6.77 seconds
Model Performance:
- Correlation: 0.09499
- Root Mean Squared Error: 3.31 mm
- Peak Error: 98.300 %
1	0.00065493	0.00031524	0.00100	33.88
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 7.07 seconds
Model Performance:
- Correlation: 0.64676
- Root Mean Squared Error: 3.15 mm
- Peak Error: 61.866 %
2	0.00015116	0.00034397	0.00099	61.35
3	0.00009734	0.00029416	0.00099	81.54
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 7.09 seconds
Model Performance:
- Correlation: 0.63892
- Root Mean Squared Error: 3.11 mm
- Peak Error: 47.702 %
4	0.00007760	0.00022746	0.00098	108.78
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 7.04 seconds
Model Performance:
- Correlation: 0.63554
- Root Mean Squared Error: 3.12 mm
- Peak Error: 52.205 %
5	0.00041942	0.00027990	0.00098	136.74
6	0.00012885	0.00020021	0.00097	156.60
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 7.40 seconds
Model Performance:
- Correlation: 0.58181
- Root Mean Squared Error: 3.16 mm
- Peak Error: 48.567 %
7	0.00014794	0.00010502	0.00097	184.14
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 7.69 seconds
Model Performance:
- Correlation: 0.66170
- Root Mean Squared Error: 3.12 mm
- Peak Error: 52.373 %
8	0.00006472	0.00012343	0.00096	211.57
9	0.00002734	0.00012268	0.00096	231.62
10	0.00009614	0.00016383	0.00095	251.17
11	0.00016928	0.00012556	0.00095	270.62
12	0.00010782	0.00010221	0.00094	290.52
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 7.61 seconds
Model Performance:
- Correlation: 0.54136
- Root Mean Squared Error: 3.22 mm
- Peak Error: 31.544 %
13	0.00004678	0.00011058	0.00094	318.68
14	0.00020952	0.00012791	0.00093	338.41
15	0.00008279	0.00013788	0.00093	358.54
16	0.00001626	0.00008309	0.00093	378.05
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 6.38 seconds
Model Performance:
- Correlation: -0.02167
- Root Mean Squared Error: 28113.80 mm
- Peak Error: 1754185.077 %
17	0.00009443	0.00008845	0.00092	403.86
18	0.00017659	0.00009198	0.00092	422.69
19	0.00001102	0.00009637	0.00091	441.52
20	0.00001717	0.00007042	0.00091	460.43
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 6.40 seconds
Model Performance:
- Correlation: 0.07292
- Root Mean Squared Error: 293.87 mm
- Peak Error: 14012.946 %
