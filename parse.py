import argparse


def parse_args():
    parser = argparse.ArgumentParser(description="Running recLSTM on bridge dataset ...")
    
    # path config
    parser.add_argument('--bridge_idx',          type=int,             default=13)
    parser.add_argument('--response',            type=str,             default='pier', help='pier or bear')
    parser.add_argument('--load_path',           type=str,             default='data/')
    parser.add_argument('--save_path',           type=str,             default='save/')
    
    # data config
    # history
    parser.add_argument('--pred_dim',            type=int,             default=2, help='dim of response')
    parser.add_argument('--dt',                  type=float,           default=0.02)
    parser.add_argument('--zero_init',           action='store_true',  default=False)
    parser.add_argument('--shift_acc',           action='store_true',  default=False)
    parser.add_argument('--flip',                action='store_true',  default=False)
    parser.add_argument('--fliplr',              action='store_true',  default=True, help='set to True if using bridge dataset. NOT fixed!')
    # slice
    parser.add_argument('--auto_slice',          action='store_true',  default=False)
    parser.add_argument('--past_history',        type=int,             default=90)
    parser.add_argument('--local_sample_step',   type=int,             default=3)
    parser.add_argument('--global_sample_step',  type=int,             default=1)
    parser.add_argument('--window_sliding_step', type=int,             default=1)
    
    # model config
    parser.add_argument('--sym',                 action='store_true',  default=False)
    parser.add_argument('--layers',              type=int,             default=4)
    parser.add_argument('--width',               type=int,             default=256)
    parser.add_argument('--split',               type=float,           default=0.95)
    
    # training config
    parser.add_argument('--epochs',              type=int,             default=2000)
    parser.add_argument('--initial_epoch',       type=int,             default=0)
    parser.add_argument('--batch_size',          type=int,             default=512)
    parser.add_argument('--initial_lr',          type=float,           default=6e-4)
    parser.add_argument('--decay',               type=float,           default=0.005)
    parser.add_argument('--resume',              type=str,             default='none')
    parser.add_argument('--loss_length',         type=int,             default=32)
    parser.add_argument('--use_amp',             type=bool,            default=True)
    
    # optimizer
    parser.add_argument('--adan',                action='store_true',  default=True)
    parser.add_argument('--max-grad-norm',       type=float,           default=1.0,   help='if the l2 norm is large than this hyper-parameter, then we clip the gradient  (default: 0.0, no gradient clip)')
    parser.add_argument('--weight-decay',        type=float,           default=0.01,   help='weight decay, similar one used in AdamW (default: 0.02)')
    parser.add_argument('--opt-eps',             type=float,           default=1e-8,  metavar='EPSILON', help='optimizer epsilon to avoid the bad case where second-order moment is zero (default: None, use opt default 1e-8 in adan)')
    parser.add_argument('--opt-betas',           type=float,           default=None,  nargs='+', metavar='BETA', help='optimizer betas in Adan (default: None, use opt default [0.98, 0.92, 0.99] in Adan)')
    parser.add_argument('--no-prox',             action='store_true',  default=False, help='whether perform weight decay like AdamW (default=False)')
    
    return parser.parse_args()