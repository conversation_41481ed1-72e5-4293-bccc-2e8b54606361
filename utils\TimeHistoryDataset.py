import numpy as np
import torch
from torch.utils.data import Dataset, Subset


class TimeHistoryDataset(Dataset):
    def __init__(self, histories, args, train=True):
        self.histories = histories
        self.story = args.pred_dim
        self.sym = args.sym
        self.flip = args.flip
        self.fliplr = args.fliplr
        self.train = train  # whether to flip the slices for augmentation
        
        self.ph = args.past_history
        self.ls = args.local_sample_step
        self.duration = self.ph * self.ls
        
        self.length = 0
        for h in histories:
            self.length += h.shape[0] - self.duration - self.ls

    def __len__(self):
        return self.length
    
    def __getitem__(self, idx):
        h_idx, idx = self.history_idx(idx)  # output idx is the index of the slice in the history
        h = self.histories[h_idx][:, :1+self.story]  # (n_step, n_dim)
        
        # data augmentation
        # if self.train and (not self.sym) and np.random.rand() > 0.5:
        #     h = -h.copy()
        #     h[:, 1:] = h[:, 1:][:, ::-1]
        
        x_acc = h[idx:idx+self.duration:self.ls, :1]
        x_abs = h[idx:idx+self.duration:self.ls, 1:]
        x_inc = x_abs - h[idx-self.ls:idx+self.duration-self.ls:self.ls, 1:]
        # x = np.concatenate([x_acc, x_abs * 0.1], -1)  # scale abs empirically according to max(inc)/max(abs)
        x = np.concatenate([x_acc, x_abs * 0.2, x_inc], -1)  # scale abs empirically according to max(inc)/max(abs)
        
        y = h[idx+self.ls:idx+self.duration+self.ls:self.ls, 1:] - x_abs  # inc after next ls
        
        x = torch.Tensor(x)
        y = torch.Tensor(y)
        return x, y
        
    def history_idx(self, idx):
        for i, h in enumerate(self.histories):
            if idx < h.shape[0] - self.duration - self.ls:
                return i, idx+self.ls
            else:
                idx -= h.shape[0] - self.duration - self.ls
        raise ValueError('idx out of range')


def split_tra_val_dataset(dataset, train_ratio=0.95, random_seed=2022):
    """
    Split a TimeHistoryDataset into training and validation sets.
    
    Args:
        dataset: TimeHistoryDataset instance
        train_ratio: Proportion of data to use for training (0.0 to 1.0)
        random_seed: Random seed for reproducibility
        
    Returns:
        train_dataset, val_dataset: Two TimeHistoryDataset subsets
    """
    # Set random seed for reproducibility
    np.random.seed(random_seed)
    
    # Get dataset size
    dataset_size = len(dataset)
    
    # Create indices and shuffle them
    indices = np.arange(dataset_size)
    np.random.shuffle(indices)
    
    # Split indices
    train_size = int(train_ratio * dataset_size)
    train_indices = indices[:train_size]
    val_indices = indices[train_size:]
    
    # Create subsets
    train_dataset = Subset(dataset, train_indices)
    val_dataset = Subset(dataset, val_indices)
    
    return train_dataset, val_dataset


