Shape of input  per step: torch.Size([64, 13])
Shape of output per step: torch.Size([64, 6])
Dataset length: 438300
Shape of input  per step: torch.Size([64, 13])
Shape of output per step: torch.Size([64, 6])
Dataset length: 401490
TrainingLSTM(
  (head): GRU(12, 256, batch_first=True)
  (hidden): ModuleList(
    (0-2): 3 x GRU(256, 256, batch_first=True)
  )
  (fc): Sequential(
    (0): Linear(in_features=256, out_features=256, bias=True)
    (1): Tanh()
    (2): Linear(in_features=256, out_features=6, bias=True)
  )
)
1458950 paramerters in total
0	0.31558859	0.31865227	0.00100	3.26
Prediction time: 13.69 seconds
Model Performance:
- Correlation: -0.02910
- Root Mean Squared Error: 1586.36 mm
- Peak Error: 73792.891 %
D:\anaconda3\envs\torch22\lib\site-packages\torch\optim\lr_scheduler.py:143: UserWarning: Detected call of `lr_scheduler.step()` before `optimizer.step()`. In PyTorch 1.1.0 and later, you should call them in the opposite order: `optimizer.step()` before `lr_scheduler.step()`.  Failure to do this will result in PyTorch skipping the first value of the learning rate schedule. See more details at https://pytorch.org/docs/stable/optim.html#how-to-adjust-learning-rate
  warnings.warn("Detected call of `lr_scheduler.step()` before `optimizer.step()`. "
1	0.00450797	0.00621108	0.00100	25.32
Prediction time: 13.49 seconds
Model Performance:
- Correlation: 0.03879
- Root Mean Squared Error: 178.96 mm
- Peak Error: 11901.565 %
2	0.00436275	0.00383010	0.00099	46.64
Prediction time: 13.62 seconds
Model Performance:
- Correlation: 0.09572
- Root Mean Squared Error: 63.39 mm
- Peak Error: 5073.524 %
3	0.00295467	0.00326502	0.00099	68.52
Prediction time: 13.09 seconds
Model Performance:
- Correlation: -0.01211
- Root Mean Squared Error: 209.32 mm
- Peak Error: 16309.816 %
4	0.00140015	0.00206982	0.00098	90.36
Prediction time: 13.93 seconds
Model Performance:
- Correlation: -0.00845
- Root Mean Squared Error: 132.15 mm
- Peak Error: 10975.675 %
5	0.00593497	0.00177899	0.00098	112.60
Prediction time: 14.46 seconds
Model Performance:
- Correlation: 0.23813
- Root Mean Squared Error: 16.64 mm
- Peak Error: 1176.556 %
6	0.00151849	0.00166492	0.00097	134.90
Prediction time: 12.45 seconds
Model Performance:
- Correlation: 0.25291
- Root Mean Squared Error: 12.49 mm
- Peak Error: 641.628 %
7	0.00208740	0.00196371	0.00097	154.96
8	0.00144713	0.00139109	0.00096	161.94
Prediction time: 11.80 seconds
Model Performance:
- Correlation: 0.25363
- Root Mean Squared Error: 16.79 mm
- Peak Error: 729.400 %
9	0.00145481	0.00140990	0.00096	181.43
10	0.00099024	0.00139120	0.00095	188.46
11	0.00138863	0.00126147	0.00095	195.47
Prediction time: 12.32 seconds
Model Performance:
- Correlation: 0.12864
- Root Mean Squared Error: 35.64 mm
- Peak Error: 2375.973 %
12	0.00080022	0.00129506	0.00094	215.51
13	0.00080154	0.00098562	0.00094	222.60
Prediction time: 12.68 seconds
Model Performance:
- Correlation: 0.03083
- Root Mean Squared Error: 175.92 mm
- Peak Error: 12427.470 %
14	0.00075458	0.00095534	0.00093	242.99
Prediction time: 12.26 seconds
Model Performance:
- Correlation: 0.06822
- Root Mean Squared Error: 90.42 mm
- Peak Error: 7250.085 %
15	0.00100089	0.00094049	0.00093	263.16
Prediction time: 11.87 seconds
Model Performance:
- Correlation: 0.09767
- Root Mean Squared Error: 18.86 mm
- Peak Error: 947.435 %
16	0.00097417	0.00069213	0.00093	282.73
Prediction time: 11.85 seconds
Model Performance:
- Correlation: 0.22192
- Root Mean Squared Error: 8.71 mm
- Peak Error: 274.950 %
17	0.00074149	0.00073154	0.00092	302.30
18	0.00070416	0.00070651	0.00092	309.36
19	0.00073716	0.00071173	0.00091	316.41
20	0.00078867	0.00074751	0.00091	323.41
21	0.00079119	0.00065114	0.00090	330.39
Prediction time: 11.68 seconds
Model Performance:
- Correlation: 0.10044
- Root Mean Squared Error: 20.65 mm
- Peak Error: 1383.116 %
22	0.00057747	0.00062722	0.00090	349.85
Prediction time: 12.08 seconds
Model Performance:
- Correlation: 0.33323
- Root Mean Squared Error: 5.81 mm
- Peak Error: 230.348 %
23	0.00047886	0.00061041	0.00090	369.77
Prediction time: 11.92 seconds
Model Performance:
- Correlation: 0.03028
- Root Mean Squared Error: 43.13 mm
- Peak Error: 3134.300 %
24	0.00059240	0.00064187	0.00089	389.53
25	0.00044110	0.00059438	0.00089	396.49
Prediction time: 11.48 seconds
Model Performance:
- Correlation: 0.02786
- Root Mean Squared Error: 46.96 mm
- Peak Error: 2371.413 %
26	0.00054506	0.00062495	0.00088	415.73
27	0.00131014	0.00067740	0.00088	422.76
28	0.00060497	0.00063019	0.00088	429.84
29	0.00066072	0.00056135	0.00087	436.85
Prediction time: 12.01 seconds
Model Performance:
- Correlation: 0.18726
- Root Mean Squared Error: 15.42 mm
- Peak Error: 671.207 %
30	0.00078001	0.00057614	0.00087	456.66
31	0.00059069	0.00063462	0.00087	463.87
32	0.00060658	0.00055410	0.00086	471.68
Prediction time: 15.66 seconds
Model Performance:
- Correlation: 0.06105
- Root Mean Squared Error: 85.33 mm
- Peak Error: 5055.811 %
33	0.00060133	0.00046881	0.00086	495.18
Prediction time: 12.94 seconds
Model Performance:
- Correlation: 0.42069
- Root Mean Squared Error: 4.14 mm
- Peak Error: 151.968 %
34	0.00029976	0.00045371	0.00085	515.81
Prediction time: 11.97 seconds
Model Performance:
- Correlation: 0.25798
- Root Mean Squared Error: 6.18 mm
- Peak Error: 324.066 %
35	0.00045114	0.00041762	0.00085	535.45
Prediction time: 13.96 seconds
Model Performance:
- Correlation: 0.08464
- Root Mean Squared Error: 44.23 mm
- Peak Error: 2565.745 %
36	0.00035003	0.00039730	0.00085	558.09
Prediction time: 14.50 seconds
Model Performance:
- Correlation: 0.29165
- Root Mean Squared Error: 6.77 mm
- Peak Error: 289.759 %
37	0.00070012	0.00042103	0.00084	580.63
38	0.00062817	0.00042316	0.00084	588.43
39	0.00028410	0.00038623	0.00084	595.50
Prediction time: 12.38 seconds
Model Performance:
- Correlation: 0.05503
- Root Mean Squared Error: 34.22 mm
- Peak Error: 2284.820 %
40	0.00035807	0.00042760	0.00083	615.66
41	0.00040729	0.00039212	0.00083	622.75
42	0.00031746	0.00039320	0.00083	629.82
43	0.00034111	0.00039467	0.00082	636.99
44	0.00038583	0.00042347	0.00082	644.28
45	0.00036159	0.00038648	0.00082	651.70
46	0.00038201	0.00043670	0.00081	659.08
47	0.00049520	0.00039993	0.00081	666.48
48	0.00045067	0.00040793	0.00081	674.22
49	0.00030469	0.00037215	0.00080	681.61
Prediction time: 14.63 seconds
Model Performance:
- Correlation: 0.19821
- Root Mean Squared Error: 5.62 mm
- Peak Error: 242.505 %
50	0.00028527	0.00038121	0.00080	704.17
51	0.00040426	0.00036462	0.00080	711.88
Prediction time: 13.54 seconds
Model Performance:
- Correlation: 0.00439
- Root Mean Squared Error: 161.24 mm
- Peak Error: 9657.219 %
52	0.00027404	0.00041841	0.00079	733.17
53	0.00027531	0.00035196	0.00079	740.17
Prediction time: 11.39 seconds
Model Performance:
- Correlation: 0.24946
- Root Mean Squared Error: 7.72 mm
- Peak Error: 328.924 %
54	0.00032680	0.00036594	0.00079	759.09
55	0.00034135	0.00037876	0.00078	766.02
56	0.00040731	0.00034154	0.00078	773.00
Prediction time: 11.45 seconds
Model Performance:
- Correlation: 0.05017
- Root Mean Squared Error: 27.58 mm
- Peak Error: 1616.144 %
57	0.00034356	0.00034593	0.00078	792.11
58	0.00041044	0.00038788	0.00078	799.10
59	0.00037353	0.00043411	0.00077	806.04
60	0.00037472	0.00035422	0.00077	813.04
61	0.00030840	0.00036909	0.00077	820.02
62	0.00039405	0.00034571	0.00076	827.09
63	0.00048219	0.00035888	0.00076	834.08
64	0.00037003	0.00034717	0.00076	841.03
65	0.00028714	0.00034315	0.00075	848.12
66	0.00036583	0.00037391	0.00075	855.20
67	0.00049236	0.00034232	0.00075	862.13
68	0.00032986	0.00032970	0.00075	869.08
Prediction time: 12.15 seconds
Model Performance:
- Correlation: 0.20460
- Root Mean Squared Error: 31.76 mm
- Peak Error: 2269.311 %
69	0.00031656	0.00033058	0.00074	888.93
70	0.00031346	0.00032064	0.00074	895.93
Prediction time: 11.58 seconds
Model Performance:
- Correlation: 0.05213
- Root Mean Squared Error: 37.95 mm
- Peak Error: 2428.006 %
71	0.00037275	0.00031555	0.00074	915.23
Prediction time: 11.59 seconds
Model Performance:
- Correlation: 0.09526
- Root Mean Squared Error: 30.51 mm
- Peak Error: 2008.584 %
72	0.00036507	0.00038981	0.00074	934.52
73	0.00029133	0.00033017	0.00073	941.43
74	0.00028660	0.00032273	0.00073	948.37
75	0.00032000	0.00033373	0.00073	955.43
76	0.00050084	0.00034933	0.00072	962.50
77	0.00049583	0.00036303	0.00072	969.44
78	0.00030174	0.00033976	0.00072	976.37
79	0.00035857	0.00033116	0.00072	983.39
80	0.00028883	0.00033426	0.00071	990.43
81	0.00033796	0.00033782	0.00071	997.34
82	0.00032357	0.00030245	0.00071	1004.23
Prediction time: 11.74 seconds
Model Performance:
- Correlation: 0.22122
- Root Mean Squared Error: 10.20 mm
- Peak Error: 671.503 %
83	0.00027084	0.00030757	0.00071	1023.65
84	0.00026965	0.00034027	0.00070	1030.59
85	0.00023496	0.00031350	0.00070	1037.57
86	0.00033005	0.00032933	0.00070	1044.49
87	0.00031230	0.00034326	0.00070	1051.49
88	0.00040042	0.00030858	0.00069	1058.46
89	0.00033218	0.00030951	0.00069	1065.47
90	0.00027853	0.00030184	0.00069	1072.38
Prediction time: 11.52 seconds
Model Performance:
- Correlation: 0.04308
- Root Mean Squared Error: 25.74 mm
- Peak Error: 2008.004 %
91	0.00022870	0.00030509	0.00069	1091.59
92	0.00027335	0.00031672	0.00068	1099.03
93	0.00028883	0.00030574	0.00068	1106.16
94	0.00027655	0.00030070	0.00068	1113.13
Prediction time: 11.54 seconds
Model Performance:
- Correlation: 0.18992
- Root Mean Squared Error: 15.54 mm
- Peak Error: 805.891 %
95	0.00036087	0.00029890	0.00068	1132.36
Prediction time: 11.55 seconds
Model Performance:
- Correlation: 0.20098
- Root Mean Squared Error: 20.27 mm
- Peak Error: 1360.591 %
96	0.00037005	0.00029705	0.00068	1151.68
Prediction time: 11.50 seconds
Model Performance:
- Correlation: 0.08135
- Root Mean Squared Error: 18.78 mm
- Peak Error: 835.746 %
97	0.00027936	0.00033420	0.00067	1170.91
98	0.00025566	0.00030077	0.00067	1177.86
99	0.00044315	0.00029770	0.00067	1184.92
100	0.00029840	0.00031480	0.00067	1192.03
101	0.00030426	0.00029748	0.00066	1199.13
102	0.00027215	0.00031179	0.00066	1206.25
103	0.00035259	0.00028707	0.00066	1213.25
Prediction time: 12.41 seconds
Model Performance:
- Correlation: 0.12864
- Root Mean Squared Error: 20.66 mm
- Peak Error: 1398.595 %
104	0.00024324	0.00029595	0.00066	1233.40
105	0.00035392	0.00031269	0.00066	1240.51
106	0.00042456	0.00029049	0.00065	1247.55
107	0.00035261	0.00030958	0.00065	1254.93
108	0.00026402	0.00029403	0.00065	1261.87
109	0.00034893	0.00027764	0.00065	1268.88
Prediction time: 11.73 seconds
Model Performance:
- Correlation: 0.22924
- Root Mean Squared Error: 6.29 mm
- Peak Error: 340.982 %
110	0.00024655	0.00029710	0.00065	1288.73
111	0.00036277	0.00029044	0.00064	1295.64
112	0.00027232	0.00028376	0.00064	1302.59
113	0.00039230	0.00029240	0.00064	1309.59
114	0.00034884	0.00028880	0.00064	1316.56
115	0.00030105	0.00030059	0.00063	1323.58
116	0.00023428	0.00029195	0.00063	1330.53
117	0.00037990	0.00027598	0.00063	1337.53
Prediction time: 12.27 seconds
Model Performance:
- Correlation: 0.12035
- Root Mean Squared Error: 14.17 mm
- Peak Error: 640.664 %
118	0.00030091	0.00028176	0.00063	1357.54
119	0.00026406	0.00027850	0.00063	1364.46
120	0.00028578	0.00031791	0.00063	1371.56
121	0.00027659	0.00033228	0.00062	1378.58
122	0.00026915	0.00029746	0.00062	1385.52
123	0.00031192	0.00027713	0.00062	1392.47
124	0.00028765	0.00027594	0.00062	1399.48
Prediction time: 11.52 seconds
Model Performance:
- Correlation: 0.21938
- Root Mean Squared Error: 5.24 mm
- Peak Error: 232.178 %
125	0.00020486	0.00028171	0.00062	1418.73
126	0.00032205	0.00028144	0.00061	1425.75
127	0.00032166	0.00028694	0.00061	1432.68
128	0.00031157	0.00026331	0.00061	1439.72
Prediction time: 11.48 seconds
Model Performance:
- Correlation: 0.01621
- Root Mean Squared Error: 151.04 mm
- Peak Error: 8712.488 %
129	0.00024333	0.00029584	0.00061	1458.91
130	0.00025686	0.00029283	0.00061	1465.86
131	0.00033450	0.00028084	0.00060	1472.89
132	0.00025715	0.00027139	0.00060	1479.87
133	0.00019530	0.00027245	0.00060	1486.88
134	0.00034700	0.00027096	0.00060	1493.82
135	0.00023110	0.00026736	0.00060	1500.81
136	0.00024854	0.00027111	0.00060	1507.77
137	0.00021319	0.00026482	0.00059	1514.77
138	0.00032662	0.00027730	0.00059	1521.69
139	0.00030300	0.00025990	0.00059	1528.63
Prediction time: 11.71 seconds
Model Performance:
- Correlation: -0.02652
- Root Mean Squared Error: 6467.02 mm
- Peak Error: 327529.617 %
140	0.00029905	0.00028595	0.00059	1548.16
141	0.00018605	0.00025266	0.00059	1555.22
Prediction time: 12.57 seconds
Model Performance:
- Correlation: -0.02653
- Root Mean Squared Error: 6613.66 mm
- Peak Error: 332945.260 %
142	0.00026395	0.00025844	0.00058	1575.43
143	0.00026038	0.00027479	0.00058	1582.35
144	0.00025203	0.00026050	0.00058	1589.38
145	0.00025403	0.00026635	0.00058	1596.33
146	0.00018207	0.00024463	0.00058	1603.30
Prediction time: 11.32 seconds
Model Performance:
- Correlation: -0.02653
- Root Mean Squared Error: 6223.08 mm
- Peak Error: 318148.275 %
147	0.00023850	0.00025421	0.00058	1622.20
148	0.00027084	0.00026297	0.00057	1629.20
149	0.00025468	0.00025749	0.00057	1636.13
150	0.00020908	0.00023394	0.00057	1643.11
Prediction time: 11.54 seconds
Model Performance:
- Correlation: -0.02651
- Root Mean Squared Error: 5989.91 mm
- Peak Error: 316235.857 %
151	0.00027087	0.00023467	0.00057	1662.39
152	0.00026433	0.00024854	0.00057	1669.36
153	0.00024867	0.00023837	0.00057	1676.39
154	0.00019621	0.00022466	0.00056	1683.43
Prediction time: 12.17 seconds
Model Performance:
- Correlation: -0.02651
- Root Mean Squared Error: 5803.27 mm
- Peak Error: 308175.030 %
155	0.00018299	0.00026277	0.00056	1703.18
156	0.00025248	0.00023382	0.00056	1710.09
157	0.00025696	0.00025246	0.00056	1717.08
158	0.00028176	0.00025050	0.00056	1724.06
159	0.00023857	0.00022838	0.00056	1730.98
160	0.00024645	0.00022862	0.00056	1737.88
161	0.00027200	0.00022400	0.00055	1744.86
Prediction time: 11.50 seconds
Model Performance:
- Correlation: -0.02571
- Root Mean Squared Error: 5878.30 mm
- Peak Error: 318991.730 %
162	0.00015145	0.00022799	0.00055	1763.97
163	0.00017320	0.00021135	0.00055	1770.96
Prediction time: 11.43 seconds
Model Performance:
- Correlation: 0.00115
- Root Mean Squared Error: 6124.06 mm
- Peak Error: 334471.024 %
164	0.00020129	0.00021320	0.00055	1789.95
165	0.00018265	0.00020514	0.00055	1796.96
Prediction time: 11.39 seconds
Model Performance:
- Correlation: -0.00154
- Root Mean Squared Error: 6259.15 mm
- Peak Error: 341923.391 %
166	0.00017164	0.00020040	0.00055	1815.98
Prediction time: 11.46 seconds
Model Performance:
- Correlation: -0.00155
- Root Mean Squared Error: 6682.05 mm
- Peak Error: 363321.502 %
167	0.00019153	0.00019696	0.00054	1835.13
Prediction time: 11.43 seconds
Model Performance:
- Correlation: 0.02851
- Root Mean Squared Error: 6611.99 mm
- Peak Error: 362194.928 %
168	0.00014740	0.00018172	0.00054	1854.24
Prediction time: 11.46 seconds
Model Performance:
- Correlation: 0.02941
- Root Mean Squared Error: 6335.98 mm
- Peak Error: 351525.087 %
169	0.00022682	0.00022602	0.00054	1873.46
170	0.00012696	0.00017654	0.00054	1880.41
Prediction time: 11.37 seconds
Model Performance:
- Correlation: 0.02677
- Root Mean Squared Error: 7274.27 mm
- Peak Error: 394585.214 %
171	0.00019384	0.00017131	0.00054	1899.52
Prediction time: 12.54 seconds
Model Performance:
- Correlation: 0.02677
- Root Mean Squared Error: 7433.81 mm
- Peak Error: 403302.332 %
172	0.00015866	0.00017367	0.00054	1919.85
173	0.00017102	0.00016732	0.00054	1926.88
Prediction time: 12.25 seconds
Model Performance:
- Correlation: 0.02674
- Root Mean Squared Error: 8462.98 mm
- Peak Error: 449730.669 %
174	0.00016800	0.00017697	0.00053	1946.80
175	0.00011324	0.00014336	0.00053	1953.72
Prediction time: 11.57 seconds
Model Performance:
- Correlation: 0.02674
- Root Mean Squared Error: 8886.10 mm
- Peak Error: 473223.612 %
176	0.00018500	0.00019245	0.00053	1972.87
177	0.00030545	0.00031344	0.00053	1979.89
178	0.00010876	0.00013001	0.00053	1986.88
Prediction time: 12.25 seconds
Model Performance:
- Correlation: 0.02674
- Root Mean Squared Error: 9672.60 mm
- Peak Error: 510986.138 %
179	0.00009965	0.00011997	0.00053	2006.84
Prediction time: 11.78 seconds
Model Performance:
- Correlation: 0.02678
- Root Mean Squared Error: 9692.32 mm
- Peak Error: 511019.766 %
180	0.00009016	0.00013112	0.00053	2026.29
181	0.00016425	0.00012353	0.00052	2033.33
182	0.00010319	0.00012052	0.00052	2040.38
183	0.00010184	0.00012065	0.00052	2047.33
184	0.00018042	0.00014158	0.00052	2054.28
185	0.00012137	0.00013201	0.00052	2061.30
186	0.00015909	0.00015520	0.00052	2068.32
187	0.00010827	0.00011334	0.00052	2075.32
Prediction time: 11.67 seconds
Model Performance:
- Correlation: 0.02672
- Root Mean Squared Error: 10425.76 mm
- Peak Error: 545526.231 %
188	0.00016282	0.00015391	0.00052	2094.63
189	0.00010730	0.00011265	0.00051	2101.69
Prediction time: 12.83 seconds
Model Performance:
- Correlation: 0.02671
- Root Mean Squared Error: 10667.38 mm
- Peak Error: 558398.484 %
190	0.00013920	0.00012986	0.00051	2122.55
191	0.00011580	0.00016112	0.00051	2129.99
192	0.00025040	0.00019879	0.00051	2137.25
193	0.00010677	0.00010441	0.00051	2144.48
Prediction time: 13.47 seconds
Model Performance:
- Correlation: 0.02673
- Root Mean Squared Error: 9080.65 mm
- Peak Error: 489166.711 %
194	0.00008181	0.00011224	0.00051	2166.03
195	0.00010760	0.00011123	0.00051	2173.52
196	0.00008888	0.00010199	0.00051	2180.60
Prediction time: 13.51 seconds
Model Performance:
- Correlation: 0.02671
- Root Mean Squared Error: 10381.68 mm
- Peak Error: 553412.868 %
197	0.00011580	0.00009862	0.00050	2202.08
Prediction time: 13.14 seconds
Model Performance:
- Correlation: 0.02672
- Root Mean Squared Error: 10992.16 mm
- Peak Error: 584703.105 %
198	0.00009660	0.00009504	0.00050	2223.31
Prediction time: 11.78 seconds
Model Performance:
- Correlation: 0.02671
- Root Mean Squared Error: 11267.09 mm
- Peak Error: 597822.001 %
199	0.00019778	0.00009859	0.00050	2242.92
200	0.00008774	0.00009258	0.00050	2249.86
Prediction time: 11.88 seconds
Model Performance:
- Correlation: 0.02670
- Root Mean Squared Error: 11163.36 mm
- Peak Error: 591421.270 %
201	0.00020604	0.00009929	0.00050	2269.58
202	0.00006370	0.00008846	0.00050	2276.79
Prediction time: 12.98 seconds
