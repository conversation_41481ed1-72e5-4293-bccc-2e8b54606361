Shape of input  per step: torch.Size([134, 5])
Shape of output per step: torch.Size([134, 2])
Dataset length: 425700
Shape of input  per step: torch.Size([134, 5])
Shape of output per step: torch.Size([134, 2])
Dataset length: 397710
TrainingLSTM(
  (head): GRU(4, 256, batch_first=True)
  (hidden): ModuleList(
    (0-2): 3 x GRU(256, 256, batch_first=True)
  )
  (fc): Sequential(
    (0): Linear(in_features=256, out_features=256, bias=True)
    (1): Tanh()
    (2): Linear(in_features=256, out_features=2, bias=True)
  )
)
1451778 paramerters in total
0	0.50031716	0.50630045	0.00100	4.40
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 17.47 seconds
Model Performance:
- Correlation: 0.00728
- Root Mean Squared Error: 3.05 mm
- Peak Error: 98.326 %
D:\anaconda3\envs\torch22\lib\site-packages\torch\optim\lr_scheduler.py:143: UserWarning: Detected call of `lr_scheduler.step()` before `optimizer.step()`. In PyTorch 1.1.0 and later, you should call them in the opposite order: `optimizer.step()` before `lr_scheduler.step()`.  Failure to do this will result in PyTorch skipping the first value of the learning rate schedule. See more details at https://pytorch.org/docs/stable/optim.html#how-to-adjust-learning-rate
  warnings.warn("Detected call of `lr_scheduler.step()` before `optimizer.step()`. "
1	0.01107886	0.01139304	0.00100	32.79
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 17.19 seconds
Model Performance:
- Correlation: 0.88035
- Root Mean Squared Error: 1.37 mm
- Peak Error: 9.585 %
2	0.00214191	0.00277969	0.00099	60.86
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 17.63 seconds
Model Performance:
- Correlation: 0.96163
- Root Mean Squared Error: 0.91 mm
- Peak Error: 8.255 %
3	0.00117436	0.00149728	0.00099	89.36
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 16.08 seconds
Model Performance:
- Correlation: 0.98484
- Root Mean Squared Error: 0.61 mm
- Peak Error: 6.530 %
4	0.00139275	0.00103163	0.00098	116.25
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 16.69 seconds
Model Performance:
- Correlation: 0.99105
- Root Mean Squared Error: 0.47 mm
- Peak Error: 5.310 %
5	0.00045500	0.00070816	0.00098	143.81
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 16.70 seconds
Model Performance:
- Correlation: 0.98912
- Root Mean Squared Error: 0.52 mm
- Peak Error: 5.211 %
6	0.00067949	0.00067739	0.00097	171.42
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 16.68 seconds
Model Performance:
- Correlation: 0.99068
- Root Mean Squared Error: 0.47 mm
- Peak Error: 4.217 %
7	0.00047594	0.00075302	0.00097	198.91
8	0.00056774	0.00071577	0.00096	209.07
9	0.00053043	0.00059143	0.00096	219.34
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 17.15 seconds
Model Performance:
- Correlation: 0.99211
- Root Mean Squared Error: 0.44 mm
- Peak Error: 3.823 %
10	0.00052082	0.00034372	0.00095	247.31
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 15.86 seconds
Model Performance:
- Correlation: 0.99041
- Root Mean Squared Error: 0.51 mm
- Peak Error: 7.872 %
11	0.00019895	0.00026837	0.00095	274.27
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 17.57 seconds
Model Performance:
- Correlation: 0.99128
- Root Mean Squared Error: 0.44 mm
- Peak Error: 6.482 %
12	0.00056896	0.00062796	0.00094	302.71
13	0.00020264	0.00024561	0.00094	313.00
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 17.72 seconds
Model Performance:
- Correlation: 0.98621
- Root Mean Squared Error: 0.72 mm
- Peak Error: 7.772 %
14	0.00021246	0.00025720	0.00093	341.49
15	0.00022148	0.00026021	0.00093	351.71
16	0.00020044	0.00023082	0.00093	361.85
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 15.91 seconds
Model Performance:
- Correlation: 0.98329
- Root Mean Squared Error: 0.71 mm
- Peak Error: 7.672 %
17	0.00033845	0.00039563	0.00092	388.61
18	0.00071956	0.00032975	0.00092	398.85
19	0.00021417	0.00019838	0.00091	409.09
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 17.28 seconds
Model Performance:
- Correlation: 0.98760
- Root Mean Squared Error: 0.61 mm
- Peak Error: 8.753 %
20	0.00052217	0.00048846	0.00091	437.44
21	0.00013792	0.00017805	0.00090	448.91
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 20.30 seconds
Model Performance:
- Correlation: 0.98918
- Root Mean Squared Error: 0.54 mm
- Peak Error: 8.218 %
22	0.00022204	0.00020349	0.00090	481.34
23	0.00031118	0.00034410	0.00090	492.76
24	0.00017077	0.00018128	0.00089	503.98
25	0.00017465	0.00019681	0.00089	515.53
26	0.00039097	0.00043486	0.00088	527.08
27	0.00016897	0.00021170	0.00088	538.25
28	0.00015815	0.00015808	0.00088	549.78
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 20.67 seconds
Model Performance:
- Correlation: 0.97925
- Root Mean Squared Error: 0.87 mm
- Peak Error: 9.554 %
29	0.00023347	0.00019449	0.00087	582.31
30	0.00034431	0.00039455	0.00087	593.53
31	0.00032824	0.00020672	0.00087	605.04
32	0.00022296	0.00020911	0.00086	616.42
33	0.00028249	0.00026281	0.00086	626.70
34	0.00015255	0.00017034	0.00085	637.07
35	0.00029995	0.00035224	0.00085	647.44
36	0.00018879	0.00016703	0.00085	657.71
37	0.00024596	0.00021815	0.00084	667.88
38	0.00023636	0.00027577	0.00084	678.20
39	0.00018863	0.00015743	0.00084	688.56
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 16.44 seconds
Model Performance:
- Correlation: 0.98607
- Root Mean Squared Error: 0.60 mm
- Peak Error: 10.039 %
40	0.00015101	0.00015938	0.00083	716.04
41	0.00014713	0.00016751	0.00083	726.27
42	0.00013735	0.00016202	0.00083	736.59
43	0.00024146	0.00028230	0.00082	747.13
44	0.00017540	0.00019913	0.00082	757.53
45	0.00040478	0.00040590	0.00082	767.91
46	0.00020223	0.00019608	0.00081	778.16
47	0.00034736	0.00035165	0.00081	788.46
48	0.00016232	0.00019381	0.00081	798.74
49	0.00011916	0.00014959	0.00080	809.45
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 17.69 seconds
Model Performance:
- Correlation: 0.98515
- Root Mean Squared Error: 0.65 mm
- Peak Error: 8.064 %
50	0.00048792	0.00018718	0.00080	837.88
51	0.00034113	0.00032265	0.00080	848.12
52	0.00024763	0.00033747	0.00079	858.45
53	0.00016071	0.00021666	0.00079	868.80
54	0.00012502	0.00014044	0.00079	879.14
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 16.90 seconds
Model Performance:
- Correlation: 0.97317
- Root Mean Squared Error: 1.06 mm
- Peak Error: 8.840 %
55	0.00012590	0.00013197	0.00078	907.03
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 17.30 seconds
Model Performance:
- Correlation: 0.98574
- Root Mean Squared Error: 0.58 mm
- Peak Error: 8.973 %
56	0.00045463	0.00046458	0.00078	935.42
57	0.00019102	0.00015205	0.00078	945.80
58	0.00014089	0.00013282	0.00078	956.06
59	0.00018199	0.00022142	0.00077	966.37
60	0.00014236	0.00016102	0.00077	976.75
61	0.00017973	0.00013432	0.00077	987.10
62	0.00010682	0.00014512	0.00076	997.43
63	0.00012978	0.00016127	0.00076	1007.75
64	0.00013480	0.00013448	0.00076	1018.16
65	0.00010962	0.00016040	0.00075	1028.49
66	0.00013312	0.00022646	0.00075	1038.77
67	0.00013552	0.00014449	0.00075	1049.01
68	0.00011064	0.00015018	0.00075	1059.23
69	0.00016872	0.00019930	0.00074	1069.62
70	0.00016275	0.00014519	0.00074	1080.06
71	0.00014343	0.00013506	0.00074	1090.35
72	0.00010915	0.00013694	0.00074	1100.68
73	0.00010378	0.00011939	0.00073	1111.06
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 17.67 seconds
Model Performance:
- Correlation: 0.98501
- Root Mean Squared Error: 0.65 mm
- Peak Error: 10.867 %
74	0.00018734	0.00019889	0.00073	1139.70
75	0.00019256	0.00020286	0.00073	1149.97
76	0.00012349	0.00012656	0.00072	1160.24
77	0.00019125	0.00011522	0.00072	1170.61
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 17.09 seconds
Model Performance:
- Correlation: 0.98237
- Root Mean Squared Error: 0.72 mm
- Peak Error: 11.394 %
78	0.00011319	0.00011901	0.00072	1198.66
79	0.00010349	0.00011684	0.00072	1209.01
80	0.00010861	0.00013209	0.00071	1219.27
81	0.00009639	0.00011074	0.00071	1229.50
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 16.52 seconds
Model Performance:
- Correlation: 0.98145
- Root Mean Squared Error: 0.67 mm
- Peak Error: 12.091 %
82	0.00008488	0.00010914	0.00071	1257.06
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 16.59 seconds
Model Performance:
- Correlation: 0.97769
- Root Mean Squared Error: 0.76 mm
- Peak Error: 12.129 %
83	0.00011359	0.00014061	0.00071	1284.75
84	0.00009220	0.00012399	0.00070	1295.02
85	0.00017628	0.00019059	0.00070	1305.35
86	0.00012942	0.00015074	0.00070	1315.67
87	0.00021902	0.00018760	0.00070	1325.93
88	0.00013766	0.00009552	0.00069	1336.14
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 16.38 seconds
Model Performance:
- Correlation: 0.97709
- Root Mean Squared Error: 0.85 mm
- Peak Error: 11.408 %
89	0.00012196	0.00011975	0.00069	1363.58
90	0.00009208	0.00009650	0.00069	1374.08
91	0.00014081	0.00016078	0.00069	1384.44
92	0.00009988	0.00009177	0.00068	1394.75
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 17.07 seconds
Model Performance:
- Correlation: 0.98088
- Root Mean Squared Error: 0.69 mm
- Peak Error: 12.588 %
93	0.00010928	0.00013624	0.00068	1422.74
94	0.00007019	0.00006532	0.00068	1433.03
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 16.39 seconds
Model Performance:
- Correlation: 0.97018
- Root Mean Squared Error: 0.89 mm
- Peak Error: 10.823 %
95	0.00015262	0.00011755	0.00068	1460.39
96	0.00007787	0.00008236	0.00068	1471.50
97	0.00008190	0.00006812	0.00067	1482.86
98	0.00016115	0.00014930	0.00067	1494.29
99	0.00004853	0.00006003	0.00067	1505.60
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 17.67 seconds
Model Performance:
- Correlation: 0.67606
- Root Mean Squared Error: 4.98 mm
- Peak Error: 21.266 %
100	0.00005231	0.00005869	0.00067	1534.19
Using symmetric recursive prediction...
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 16.54 seconds
Model Performance:
- Correlation: 0.96247
- Root Mean Squared Error: 0.97 mm
- Peak Error: 14.651 %
101	0.00009939	0.00008726	0.00066	1561.80
102	0.00017720	0.00007173	0.00066	1572.11
103	0.00008277	0.00009934	0.00066	1582.47
