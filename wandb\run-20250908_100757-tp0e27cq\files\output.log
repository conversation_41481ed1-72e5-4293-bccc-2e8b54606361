Shape of input  per step: torch.Size([148, 5])
Shape of output per step: torch.Size([148, 2])
Dataset length: 423180
Shape of input  per step: torch.Size([148, 5])
Shape of output per step: torch.Size([148, 2])
Dataset length: 396954
TrainingRNN(
  (head): GRU(4, 256, batch_first=True)
  (hidden): ModuleList(
    (0-2): 3 x GRU(256, 256, batch_first=True)
  )
  (fc): Sequential(
    (0): Linear(in_features=256, out_features=256, bias=True)
    (1): Tanh()
    (2): Linear(in_features=256, out_features=2, bias=True)
  )
)
1451778 paramerters in total
0	0.59284437	0.62286222	0.00060	4.65
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.20 seconds
Model Performance:
- Correlation: 0.00634
- Root Mean Squared Error: 11.35 mm
- Peak Error: 128.781 %
D:\anaconda3\envs\torch22\lib\site-packages\torch\optim\lr_scheduler.py:143: UserWarning: Detected call of `lr_scheduler.step()` before `optimizer.step()`. In PyTorch 1.1.0 and later, you should call them in the opposite order: `optimizer.step()` before `lr_scheduler.step()`.  Failure to do this will result in PyTorch skipping the first value of the learning rate schedule. See more details at https://pytorch.org/docs/stable/optim.html#how-to-adjust-learning-rate
  warnings.warn("Detected call of `lr_scheduler.step()` before `optimizer.step()`. "
1	0.01162701	0.01198291	0.00060	32.64
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.86 seconds
Model Performance:
- Correlation: 0.89415
- Root Mean Squared Error: 2.00 mm
- Peak Error: 10.257 %
2	0.00753904	0.00745416	0.00059	62.00
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.46 seconds
Model Performance:
- Correlation: 0.93555
- Root Mean Squared Error: 1.60 mm
- Peak Error: 10.771 %
3	0.00504778	0.00519178	0.00059	93.08
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.18 seconds
Model Performance:
- Correlation: 0.94463
- Root Mean Squared Error: 1.53 mm
- Peak Error: 9.653 %
4	0.00334766	0.00315032	0.00059	120.80
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.53 seconds
Model Performance:
- Correlation: 0.96289
- Root Mean Squared Error: 1.28 mm
- Peak Error: 10.481 %
5	0.00099357	0.00112976	0.00059	149.13
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.43 seconds
Model Performance:
- Correlation: 0.97571
- Root Mean Squared Error: 1.02 mm
- Peak Error: 6.007 %
6	0.00086163	0.00089594	0.00058	178.24
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.80 seconds
Model Performance:
- Correlation: 0.98062
- Root Mean Squared Error: 0.92 mm
- Peak Error: 4.107 %
7	0.00129199	0.00098323	0.00058	206.95
8	0.00128847	0.00131910	0.00058	218.09
9	0.00069496	0.00062091	0.00057	229.27
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.27 seconds
Model Performance:
- Correlation: 0.97967
- Root Mean Squared Error: 1.04 mm
- Peak Error: 5.519 %
10	0.00088127	0.00075420	0.00057	257.44
11	0.00046683	0.00048371	0.00057	268.71
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.38 seconds
Model Performance:
- Correlation: 0.98334
- Root Mean Squared Error: 0.86 mm
- Peak Error: 3.425 %
12	0.00032432	0.00034338	0.00057	296.89
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.75 seconds
Model Performance:
- Correlation: 0.98337
- Root Mean Squared Error: 0.89 mm
- Peak Error: 3.619 %
13	0.00041903	0.00041798	0.00056	325.55
14	0.00054015	0.00043106	0.00056	336.88
15	0.00030760	0.00034176	0.00056	348.33
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.39 seconds
Model Performance:
- Correlation: 0.98215
- Root Mean Squared Error: 0.99 mm
- Peak Error: 5.281 %
16	0.00018529	0.00022335	0.00056	376.75
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.48 seconds
Model Performance:
- Correlation: 0.98255
- Root Mean Squared Error: 0.99 mm
- Peak Error: 4.612 %
17	0.00044177	0.00037120	0.00055	406.10
18	0.00047953	0.00038511	0.00055	417.41
19	0.00039274	0.00028732	0.00055	429.37
20	0.00018232	0.00020597	0.00055	441.66
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 21.45 seconds
Model Performance:
- Correlation: 0.97807
- Root Mean Squared Error: 1.10 mm
- Peak Error: 5.693 %
21	0.00020020	0.00022298	0.00054	476.35
22	0.00020621	0.00022093	0.00054	488.66
23	0.00047874	0.00053490	0.00054	501.05
24	0.00024890	0.00028594	0.00054	513.33
25	0.00015160	0.00017555	0.00053	525.72
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 21.56 seconds
Model Performance:
- Correlation: 0.97943
- Root Mean Squared Error: 1.04 mm
- Peak Error: 3.757 %
26	0.00021621	0.00023079	0.00053	560.62
27	0.00026452	0.00027638	0.00053	573.31
28	0.00016949	0.00016196	0.00053	586.03
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 20.54 seconds
Model Performance:
- Correlation: 0.98194
- Root Mean Squared Error: 0.97 mm
- Peak Error: 4.508 %
29	0.00021179	0.00020781	0.00052	619.83
30	0.00028651	0.00021051	0.00052	632.19
31	0.00033426	0.00030746	0.00052	643.69
32	0.00022056	0.00023264	0.00052	656.15
33	0.00020753	0.00021027	0.00052	668.64
34	0.00022182	0.00015584	0.00051	681.40
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 20.96 seconds
Model Performance:
- Correlation: 0.97734
- Root Mean Squared Error: 1.10 mm
- Peak Error: 4.275 %
35	0.00020801	0.00023900	0.00051	715.86
36	0.00019441	0.00022367	0.00051	728.17
37	0.00026821	0.00028475	0.00051	741.34
38	0.00033297	0.00040624	0.00050	753.75
39	0.00022603	0.00018277	0.00050	766.57
40	0.00012529	0.00013665	0.00050	779.28
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 20.80 seconds
Model Performance:
- Correlation: 0.98107
- Root Mean Squared Error: 0.99 mm
- Peak Error: 4.595 %
41	0.00031926	0.00015048	0.00050	813.28
42	0.00016921	0.00012778	0.00050	824.89
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 20.09 seconds
Model Performance:
- Correlation: 0.98103
- Root Mean Squared Error: 1.00 mm
- Peak Error: 4.722 %
43	0.00011739	0.00013714	0.00049	856.62
44	0.00021503	0.00023411	0.00049	867.72
45	0.00011971	0.00013747	0.00049	878.94
46	0.00022463	0.00015685	0.00049	890.74
47	0.00022344	0.00012791	0.00049	902.98
48	0.00017833	0.00018029	0.00048	915.09
49	0.00017596	0.00014808	0.00048	926.68
50	0.00013094	0.00011569	0.00048	938.71
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.78 seconds
Model Performance:
- Correlation: 0.97914
- Root Mean Squared Error: 1.08 mm
- Peak Error: 6.782 %
51	0.00010444	0.00012254	0.00048	969.52
52	0.00012413	0.00011732	0.00048	980.68
53	0.00008645	0.00009219	0.00047	991.85
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.43 seconds
Model Performance:
- Correlation: 0.98276
- Root Mean Squared Error: 0.98 mm
- Peak Error: 4.916 %
54	0.00011763	0.00016592	0.00047	1020.03
55	0.00019872	0.00007163	0.00047	1031.49
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.91 seconds
Model Performance:
- Correlation: 0.97791
- Root Mean Squared Error: 1.08 mm
- Peak Error: 5.397 %
56	0.00009379	0.00009456	0.00047	1062.11
57	0.00019991	0.00019899	0.00047	1073.34
58	0.00005315	0.00009594	0.00047	1084.70
59	0.00010806	0.00010444	0.00046	1095.86
60	0.00011156	0.00009146	0.00046	1106.91
61	0.00034839	0.00032665	0.00046	1118.01
62	0.00005158	0.00006981	0.00046	1129.19
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.45 seconds
Model Performance:
- Correlation: 0.97616
- Root Mean Squared Error: 1.14 mm
- Peak Error: 6.553 %
63	0.00006899	0.00006890	0.00046	1157.38
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.12 seconds
Model Performance:
- Correlation: 0.97488
- Root Mean Squared Error: 1.16 mm
- Peak Error: 6.155 %
64	0.00006747	0.00007989	0.00045	1185.30
65	0.00017319	0.00019422	0.00045	1197.51
66	0.00007929	0.00007208	0.00045	1209.15
67	0.00017233	0.00007701	0.00045	1220.38
68	0.00005485	0.00007080	0.00045	1231.62
69	0.00006551	0.00006371	0.00045	1242.71
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.67 seconds
Model Performance:
- Correlation: 0.97622
- Root Mean Squared Error: 1.15 mm
- Peak Error: 7.363 %
70	0.00011638	0.00007022	0.00044	1271.18
71	0.00007175	0.00009006	0.00044	1282.29
72	0.00044142	0.00032682	0.00044	1293.67
73	0.00006022	0.00006837	0.00044	1304.77
74	0.00015475	0.00018069	0.00044	1316.11
75	0.00006892	0.00007255	0.00044	1328.74
76	0.00011632	0.00013518	0.00043	1341.51
77	0.00009134	0.00010835	0.00043	1354.01
78	0.00009123	0.00009088	0.00043	1367.61
79	0.00014677	0.00015407	0.00043	1381.75
80	0.00006162	0.00006534	0.00043	1394.12
81	0.00014573	0.00012012	0.00043	1406.39
82	0.00010299	0.00006852	0.00043	1418.65
83	0.00011131	0.00012928	0.00042	1431.02
84	0.00011360	0.00010655	0.00042	1442.94
85	0.00010184	0.00011046	0.00042	1455.19
86	0.00006347	0.00006451	0.00042	1467.18
87	0.00006219	0.00007442	0.00042	1479.35
88	0.00015085	0.00008509	0.00042	1491.29
89	0.00006369	0.00008302	0.00042	1503.54
90	0.00004460	0.00006125	0.00041	1516.12
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 26.07 seconds
Model Performance:
- Correlation: 0.96455
- Root Mean Squared Error: 1.43 mm
- Peak Error: 9.774 %
91	0.00006968	0.00008929	0.00041	1556.01
92	0.00016598	0.00013469	0.00041	1568.45
93	0.00002905	0.00005452	0.00041	1580.72
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 21.03 seconds
Model Performance:
- Correlation: 0.97020
- Root Mean Squared Error: 1.25 mm
- Peak Error: 5.291 %
94	0.00007109	0.00005679	0.00041	1614.45
95	0.00004818	0.00007249	0.00041	1626.61
96	0.00006580	0.00006249	0.00041	1638.56
97	0.00009961	0.00010759	0.00040	1650.27
98	0.00007452	0.00006669	0.00040	1662.66
99	0.00007834	0.00008442	0.00040	1675.32
100	0.00011088	0.00007578	0.00040	1688.39
101	0.00015164	0.00005741	0.00040	1701.71
102	0.00006300	0.00006359	0.00040	1715.17
103	0.00006755	0.00005670	0.00040	1728.29
104	0.00005979	0.00006780	0.00039	1741.47
105	0.00005806	0.00007478	0.00039	1754.98
106	0.00010198	0.00005830	0.00039	1768.57
107	0.00004460	0.00006303	0.00039	1781.91
108	0.00009292	0.00005943	0.00039	1795.26
109	0.00007241	0.00007304	0.00039	1808.21
110	0.00009822	0.00009283	0.00039	1821.37
111	0.00004180	0.00005362	0.00039	1835.76
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 24.85 seconds
Model Performance:
- Correlation: 0.95313
- Root Mean Squared Error: 1.49 mm
- Peak Error: 7.149 %
112	0.00005138	0.00005465	0.00038	1873.38
113	0.00007011	0.00006741	0.00038	1885.48
114	0.00006407	0.00005462	0.00038	1897.95
115	0.00008190	0.00006878	0.00038	1910.32
116	0.00006769	0.00007490	0.00038	1922.19
117	0.00006488	0.00006361	0.00038	1934.99
118	0.00005067	0.00006403	0.00038	1947.26
119	0.00004225	0.00005170	0.00038	1959.94
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 27.89 seconds
Model Performance:
- Correlation: 0.97095
- Root Mean Squared Error: 1.19 mm
- Peak Error: 5.122 %
120	0.00009986	0.00008194	0.00037	2000.76
121	0.00005254	0.00005010	0.00037	2013.26
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.12 seconds
Model Performance:
- Correlation: 0.97455
- Root Mean Squared Error: 1.16 mm
- Peak Error: 4.878 %
122	0.00005994	0.00006713	0.00037	2045.86
123	0.00003239	0.00005117	0.00037	2059.03
124	0.00005747	0.00005565	0.00037	2073.02
125	0.00004609	0.00005157	0.00037	2085.77
126	0.00005025	0.00004799	0.00037	2098.07
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 21.89 seconds
Model Performance:
- Correlation: 0.95645
- Root Mean Squared Error: 1.47 mm
- Peak Error: 7.311 %
127	0.00020538	0.00016238	0.00037	2133.05
128	0.00013265	0.00010417	0.00037	2145.18
129	0.00005407	0.00006466	0.00036	2157.26
130	0.00003671	0.00004999	0.00036	2169.58
131	0.00008607	0.00009481	0.00036	2181.95
132	0.00007088	0.00007203	0.00036	2194.67
133	0.00005380	0.00005443	0.00036	2206.59
134	0.00005093	0.00004905	0.00036	2217.72
135	0.00005279	0.00005439	0.00036	2228.75
136	0.00004652	0.00005329	0.00036	2239.89
137	0.00006307	0.00005922	0.00036	2251.14
138	0.00003636	0.00004804	0.00036	2262.40
139	0.00012730	0.00015008	0.00035	2274.93
140	0.00005847	0.00006581	0.00035	2286.50
141	0.00008388	0.00005587	0.00035	2298.28
142	0.00008001	0.00007849	0.00035	2310.25
143	0.00005830	0.00005886	0.00035	2321.84
144	0.00004028	0.00005620	0.00035	2333.24
145	0.00005891	0.00006199	0.00035	2345.29
146	0.00010949	0.00011146	0.00035	2356.63
