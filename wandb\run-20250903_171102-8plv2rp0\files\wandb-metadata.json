{"os": "Windows-10-10.0.26100-SP0", "python": "CPython 3.9.18", "startedAt": "2025-09-03T09:11:02.272351Z", "args": ["--bridge_idx=33", "--response=bear", "--past_history=120", "--loss_length=40", "--adan", "--layers=4", "--pred_dim=1"], "program": "E:\\Codes\\recLSTM-bridge\\train.py", "codePath": "train.py", "codePathLocal": "train.py", "email": "<EMAIL>", "root": "E:\\Codes\\recLSTM-bridge", "host": "DESKTOP-PB9IBRM", "executable": "D:\\anaconda3\\envs\\torch22\\python.exe", "cpu_count": 14, "cpu_count_logical": 20, "gpu": "NVIDIA GeForce RTX 4060 Ti", "gpu_count": 1, "disk": {"/": {"total": "************", "used": "************"}}, "memory": {"total": "34188517376"}, "gpu_nvidia": [{"name": "NVIDIA GeForce RTX 4060 Ti", "memoryTotal": "17175674880", "cudaCores": 4352, "architecture": "Ada", "uuid": "GPU-d0fde4af-f8a8-fdca-3317-74078f49b557"}], "cudaVersion": "12.6", "writerId": "r8rgaz5ago9axd6wgmgwwjnd84bkkyci"}