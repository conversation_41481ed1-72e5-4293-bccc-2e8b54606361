Shape of input  per step: torch.Size([120, 5])
Shape of output per step: torch.Size([120, 2])
Dataset length: 834660
Shape of input  per step: torch.Size([120, 5])
Shape of output per step: torch.Size([120, 2])
Dataset length: 395199
TrainingRNN(
  (head): SymmetrizedBlock(
    (rnn): GRU(4, 256, batch_first=True)
  )
  (hidden): ModuleList(
    (0-2): 3 x SymmetrizedBlock(
      (rnn): GRU(256, 256, batch_first=True)
    )
  )
  (fc): Sequential(
    (0): Linear(in_features=256, out_features=256, bias=False)
    (1): Tanh()
    (2): Linear(in_features=256, out_features=2, bias=False)
  )
)
1451520 paramerters in total
0	0.00033879	0.00046178	0.00060	7.17
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.54 seconds
Model Performance:
- Correlation: -0.20475
- Root Mean Squared Error: 1.64 mm
- Peak Error: 98.614 %
1	0.00009932	0.00020116	0.00060	46.18
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 21.04 seconds
Model Performance:
- Correlation: 0.59836
- Root Mean Squared Error: 1.52 mm
- Peak Error: 33.500 %
2	0.00028646	0.00030656	0.00059	87.77
3	0.00035795	0.00034008	0.00059	107.94
4	0.00026714	0.00021688	0.00059	127.42
5	0.00014716	0.00023173	0.00059	146.27
6	0.00006718	0.00020379	0.00058	165.03
7	0.00017190	0.00024220	0.00058	183.87
8	0.00023343	0.00024078	0.00058	202.69
9	0.00012844	0.00021370	0.00057	221.57
10	0.00012882	0.00018709	0.00057	240.28
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.85 seconds
Model Performance:
- Correlation: 0.17604
- Root Mean Squared Error: 331.68 mm
- Peak Error: 30394.639 %
11	0.00031478	0.00016107	0.00057	278.54
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.87 seconds
Model Performance:
- Correlation: 0.69809
- Root Mean Squared Error: 1.46 mm
- Peak Error: 36.945 %
12	0.00012636	0.00012140	0.00057	315.85
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.31 seconds
Model Performance:
- Correlation: 0.01794
- Root Mean Squared Error: 166865.01 mm
- Peak Error: 7771350.473 %
13	0.00008228	0.00011355	0.00056	352.65
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.50 seconds
Model Performance:
- Correlation: 0.03299
- Root Mean Squared Error: 74951.90 mm
- Peak Error: 4330416.405 %
14	0.00006088	0.00010847	0.00056	390.58
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.72 seconds
Model Performance:
- Correlation: 0.57940
- Root Mean Squared Error: 1.63 mm
- Peak Error: 40.350 %
15	0.00004707	0.00014134	0.00056	429.40
16	0.00004051	0.00010468	0.00056	449.84
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.02 seconds
Model Performance:
- Correlation: 0.84022
- Root Mean Squared Error: 1.17 mm
- Peak Error: 30.217 %
17	0.00002751	0.00011040	0.00055	488.86
18	0.00020754	0.00011194	0.00055	508.08
19	0.00005686	0.00011950	0.00055	527.25
20	0.00003896	0.00009813	0.00055	546.71
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 20.11 seconds
Model Performance:
- Correlation: 0.65884
- Root Mean Squared Error: 1.82 mm
- Peak Error: 18.155 %
21	0.00007419	0.00007324	0.00054	586.25
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.36 seconds
Model Performance:
- Correlation: 0.59872
- Root Mean Squared Error: 1.67 mm
- Peak Error: 32.900 %
22	0.00001161	0.00013100	0.00054	623.08
23	0.00001104	0.00006937	0.00054	641.77
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.66 seconds
Model Performance:
- Correlation: -0.00142
- Root Mean Squared Error: 187860.89 mm
- Peak Error: 8034114.992 %
24	0.00003783	0.00009222	0.00054	679.24
25	0.00005424	0.00007227	0.00053	701.74
26	0.00003796	0.00009034	0.00053	726.72
27	0.00001304	0.00006841	0.00053	749.70
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.80 seconds
Model Performance:
- Correlation: 0.75070
- Root Mean Squared Error: 1.30 mm
- Peak Error: 32.008 %
28	0.00002428	0.00007036	0.00053	788.37
29	0.00001189	0.00008501	0.00052	807.17
30	0.00011229	0.00007152	0.00052	826.00
31	0.00004379	0.00015035	0.00052	844.71
32	0.00001254	0.00012388	0.00052	863.44
33	0.00001825	0.00007135	0.00052	882.21
34	0.00008088	0.00009090	0.00051	901.28
35	0.00004104	0.00008038	0.00051	920.14
36	0.00003062	0.00008356	0.00051	938.89
37	0.00015149	0.00007498	0.00051	957.75
38	0.00010103	0.00011903	0.00050	976.62
39	0.00000855	0.00012351	0.00050	995.52
40	0.00010243	0.00012430	0.00050	1014.32
41	0.00001983	0.00012122	0.00050	1033.11
42	0.00003627	0.00009996	0.00050	1052.02
43	0.00001318	0.00008242	0.00049	1070.91
44	0.00002433	0.00006163	0.00049	1089.70
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.06 seconds
Model Performance:
- Correlation: 0.71411
- Root Mean Squared Error: 1.32 mm
- Peak Error: 18.933 %
45	0.00003016	0.00010989	0.00049	1128.30
46	0.00000754	0.00010766	0.00049	1147.37
47	0.00005574	0.00013606	0.00049	1166.30
48	0.00001877	0.00009184	0.00048	1187.10
49	0.00001205	0.00014018	0.00048	1211.10
50	0.00008165	0.00010765	0.00048	1234.71
51	0.00001572	0.00006491	0.00048	1253.76
52	0.00005102	0.00010306	0.00048	1272.80
53	0.00002031	0.00006724	0.00047	1291.94
54	0.00004575	0.00006327	0.00047	1310.75
55	0.00001875	0.00013779	0.00047	1329.58
56	0.00003367	0.00007962	0.00047	1348.45
57	0.00003456	0.00009879	0.00047	1367.20
58	0.00006227	0.00006692	0.00047	1385.95
59	0.00012473	0.00006696	0.00046	1404.83
60	0.00006801	0.00009613	0.00046	1423.67
61	0.00000674	0.00005694	0.00046	1442.38
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.18 seconds
Model Performance:
- Correlation: 0.82589
- Root Mean Squared Error: 1.17 mm
- Peak Error: 16.414 %
62	0.00000976	0.00012183	0.00046	1480.97
63	0.00016417	0.00009993	0.00046	1500.20
64	0.00012616	0.00009272	0.00045	1520.32
65	0.00002041	0.00008644	0.00045	1539.56
66	0.00002105	0.00008817	0.00045	1558.30
67	0.00009791	0.00009977	0.00045	1577.11
68	0.00004921	0.00007317	0.00045	1595.92
69	0.00021477	0.00009154	0.00045	1614.76
70	0.00001715	0.00011875	0.00044	1633.51
71	0.00003433	0.00007570	0.00044	1652.35
72	0.00008041	0.00007927	0.00044	1671.19
73	0.00001614	0.00007749	0.00044	1690.34
74	0.00001794	0.00005916	0.00044	1709.12
75	0.00005378	0.00011072	0.00044	1727.97
76	0.00020793	0.00008576	0.00043	1746.82
77	0.00006646	0.00009201	0.00043	1765.68
78	0.00000321	0.00008551	0.00043	1785.49
79	0.00000868	0.00008602	0.00043	1804.59
80	0.00001972	0.00007899	0.00043	1823.44
81	0.00000560	0.00009916	0.00043	1842.38
82	0.00001921	0.00009844	0.00043	1861.31
83	0.00009055	0.00010580	0.00042	1880.03
84	0.00011824	0.00010489	0.00042	1898.85
85	0.00001350	0.00012554	0.00042	1917.68
86	0.00002653	0.00008067	0.00042	1937.26
87	0.00009603	0.00013311	0.00042	1956.17
88	0.00004329	0.00011355	0.00042	1975.03
89	0.00036563	0.00010372	0.00042	1993.84
90	0.00008489	0.00008913	0.00041	2012.88
91	0.00005018	0.00008332	0.00041	2032.46
92	0.00001742	0.00007356	0.00041	2052.37
93	0.00002369	0.00010937	0.00041	2072.69
94	0.00001950	0.00009323	0.00041	2092.93
95	0.00003966	0.00009423	0.00041	2112.74
96	0.00003551	0.00009814	0.00041	2131.57
97	0.00005880	0.00009389	0.00040	2150.45
98	0.00003132	0.00014430	0.00040	2169.34
99	0.00010456	0.00008605	0.00040	2188.10
100	0.00013247	0.00007384	0.00040	2206.91
