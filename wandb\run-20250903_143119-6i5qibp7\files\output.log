Shape of input  per step: torch.Size([120, 13])
Shape of output per step: torch.Size([120, 6])
Dataset length: 417330
Shape of input  per step: torch.Size([120, 13])
Shape of output per step: torch.Size([120, 6])
Dataset length: 395199
TrainingLSTM(
  (head): GRU(12, 256, batch_first=True)
  (hidden): ModuleList(
    (0-1): 2 x GRU(256, 256, batch_first=True)
  )
  (fc): Sequential(
    (0): Linear(in_features=256, out_features=256, bias=True)
    (1): Tanh()
    (2): Linear(in_features=256, out_features=6, bias=True)
  )
)
1064198 paramerters in total
0	3.20361686	3.19531107	0.00100	3.84
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 11.12 seconds
Model Performance:
- Correlation: -0.00613
- Root Mean Squared Error: 2942.87 mm
- Peak Error: 103898.940 %
D:\anaconda3\envs\torch22\lib\site-packages\torch\optim\lr_scheduler.py:143: UserWarning: Detected call of `lr_scheduler.step()` before `optimizer.step()`. In PyTorch 1.1.0 and later, you should call them in the opposite order: `optimizer.step()` before `lr_scheduler.step()`.  Failure to do this will result in PyTorch skipping the first value of the learning rate schedule. See more details at https://pytorch.org/docs/stable/optim.html#how-to-adjust-learning-rate
  warnings.warn("Detected call of `lr_scheduler.step()` before `optimizer.step()`. "
1	0.00735434	0.00638843	0.00100	25.05
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 13.43 seconds
Model Performance:
- Correlation: 0.02029
- Root Mean Squared Error: 77.14 mm
- Peak Error: 4972.332 %
2	0.00505563	0.00401623	0.00099	48.88
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 14.88 seconds
Model Performance:
- Correlation: 0.01437
- Root Mean Squared Error: 347.11 mm
- Peak Error: 16476.838 %
3	0.00212718	0.00226736	0.00099	73.86
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 14.03 seconds
Model Performance:
- Correlation: 0.09545
- Root Mean Squared Error: 25.66 mm
- Peak Error: 850.585 %
4	0.00198156	0.00179636	0.00098	98.85
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 14.39 seconds
Model Performance:
- Correlation: 0.49520
- Root Mean Squared Error: 3.54 mm
- Peak Error: 47.133 %
5	0.00252039	0.00268438	0.00098	123.94
6	0.00242255	0.00221691	0.00097	134.64
7	0.00117876	0.00127196	0.00097	145.02
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 15.24 seconds
Model Performance:
- Correlation: 0.26392
- Root Mean Squared Error: 8.35 mm
- Peak Error: 308.106 %
8	0.00143571	0.00130148	0.00096	171.53
9	0.00143886	0.00133853	0.00096	181.73
10	0.00132247	0.00148880	0.00095	191.81
11	0.00168399	0.00162114	0.00095	201.83
12	0.00111389	0.00119538	0.00094	211.07
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 11.12 seconds
Model Performance:
- Correlation: 0.06513
- Root Mean Squared Error: 36.49 mm
- Peak Error: 1578.017 %
13	0.00104738	0.00118276	0.00094	232.01
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 10.87 seconds
Model Performance:
- Correlation: 0.50088
- Root Mean Squared Error: 3.99 mm
- Peak Error: 86.133 %
14	0.00125160	0.00094925	0.00093	252.29
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 11.24 seconds
Model Performance:
- Correlation: 0.45669
- Root Mean Squared Error: 6.69 mm
- Peak Error: 137.455 %
15	0.00190374	0.00102412	0.00093	272.91
16	0.00080656	0.00099763	0.00093	281.53
17	0.00068324	0.00080997	0.00092	290.11
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 11.37 seconds
Model Performance:
- Correlation: 0.41052
- Root Mean Squared Error: 5.05 mm
- Peak Error: 120.183 %
18	0.00080784	0.00088437	0.00092	311.73
19	0.00065616	0.00068228	0.00091	321.15
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 11.94 seconds
Model Performance:
- Correlation: 0.18057
- Root Mean Squared Error: 15.03 mm
- Peak Error: 451.402 %
20	0.00087631	0.00101295	0.00091	343.28
21	0.00051118	0.00060927	0.00090	353.81
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 13.70 seconds
Model Performance:
- Correlation: 0.17531
- Root Mean Squared Error: 12.63 mm
- Peak Error: 550.349 %
22	0.00049808	0.00054064	0.00090	378.35
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 12.31 seconds
Model Performance:
- Correlation: 0.35207
- Root Mean Squared Error: 4.72 mm
- Peak Error: 176.535 %
23	0.00103171	0.00094698	0.00090	400.87
24	0.00090007	0.00072857	0.00089	410.35
25	0.00068352	0.00061179	0.00089	419.86
26	0.00049578	0.00050369	0.00088	430.37
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 13.25 seconds
Model Performance:
- Correlation: 0.39460
- Root Mean Squared Error: 8.02 mm
- Peak Error: 259.361 %
27	0.00053470	0.00058894	0.00088	453.71
28	0.00075990	0.00068280	0.00088	463.11
29	0.00050157	0.00047869	0.00087	472.67
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 11.43 seconds
Model Performance:
- Correlation: 0.34805
- Root Mean Squared Error: 8.91 mm
- Peak Error: 289.889 %
30	0.00072629	0.00075601	0.00087	494.21
31	0.00043666	0.00043934	0.00087	503.74
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 13.85 seconds
Model Performance:
- Correlation: 0.28914
- Root Mean Squared Error: 8.15 mm
- Peak Error: 219.627 %
32	0.00034186	0.00039075	0.00086	527.88
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 12.79 seconds
Model Performance:
- Correlation: 0.62029
- Root Mean Squared Error: 1.89 mm
- Peak Error: 20.053 %
33	0.00052250	0.00045747	0.00086	551.02
34	0.00040306	0.00038888	0.00085	560.43
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 13.91 seconds
Model Performance:
- Correlation: 0.43025
- Root Mean Squared Error: 4.35 mm
- Peak Error: 90.873 %
35	0.00039596	0.00044411	0.00085	585.40
36	0.00056415	0.00044444	0.00085	594.91
37	0.00032968	0.00034851	0.00084	604.33
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 11.69 seconds
Model Performance:
- Correlation: 0.48357
- Root Mean Squared Error: 4.03 mm
- Peak Error: 85.479 %
38	0.00041858	0.00036548	0.00084	626.08
39	0.00049129	0.00038930	0.00084	635.46
40	0.00038095	0.00044191	0.00083	644.79
41	0.00044454	0.00037946	0.00083	654.04
42	0.00044643	0.00045293	0.00083	663.29
43	0.00074371	0.00072037	0.00082	672.67
44	0.00034576	0.00032844	0.00082	682.31
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 12.90 seconds
Model Performance:
- Correlation: 0.27842
- Root Mean Squared Error: 9.81 mm
- Peak Error: 368.117 %
45	0.00037583	0.00030379	0.00082	705.47
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 12.28 seconds
Model Performance:
- Correlation: 0.64481
- Root Mean Squared Error: 1.97 mm
- Peak Error: 24.726 %
46	0.00050022	0.00046199	0.00081	728.67
47	0.00038386	0.00031973	0.00081	738.28
48	0.00061783	0.00061679	0.00081	747.59
49	0.00026285	0.00029852	0.00080	757.12
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 11.70 seconds
Model Performance:
- Correlation: 0.46012
- Root Mean Squared Error: 5.31 mm
- Peak Error: 117.564 %
50	0.00044434	0.00033282	0.00080	779.78
51	0.00034827	0.00039065	0.00080	789.11
52	0.00026335	0.00031937	0.00079	798.60
53	0.00027619	0.00027452	0.00079	808.03
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 12.80 seconds
Model Performance:
- Correlation: 0.66237
- Root Mean Squared Error: 2.70 mm
- Peak Error: 51.293 %
54	0.00036738	0.00032914	0.00079	830.54
55	0.00033530	0.00031633	0.00078	839.81
56	0.00037309	0.00038049	0.00078	849.26
57	0.00034185	0.00033985	0.00078	858.78
58	0.00037076	0.00030302	0.00078	868.13
59	0.00024028	0.00029456	0.00077	877.59
60	0.00023032	0.00027085	0.00077	887.11
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 12.51 seconds
Model Performance:
- Correlation: 0.37286
- Root Mean Squared Error: 7.30 mm
- Peak Error: 137.424 %
61	0.00032670	0.00030362	0.00077	909.95
62	0.00025120	0.00030438	0.00076	919.50
63	0.00030397	0.00028817	0.00076	928.79
64	0.00033180	0.00031846	0.00076	938.47
65	0.00041313	0.00030735	0.00075	947.96
66	0.00026348	0.00025950	0.00075	957.88
Loading single model from epoch best...
Model weights loaded successfully.
Prediction time: 12.98 seconds
Model Performance:
- Correlation: 0.41716
- Root Mean Squared Error: 3.65 mm
- Peak Error: 66.701 %
67	0.00028670	0.00032847	0.00075	980.21
68	0.00033856	0.00027132	0.00075	989.45
