Shape of input  per step: torch.Size([120, 5])
Shape of output per step: torch.Size([120, 2])
Dataset length: 834660
Shape of input  per step: torch.Size([120, 5])
Shape of output per step: torch.Size([120, 2])
Dataset length: 395199
TrainingRNN(
  (head): GRU(4, 256, batch_first=True)
  (hidden): ModuleList(
    (0-2): 3 x GRU(256, 256, batch_first=True)
  )
  (fc): Sequential(
    (0): Linear(in_features=256, out_features=256, bias=True)
    (1): Tanh()
    (2): Linear(in_features=256, out_features=2, bias=True)
  )
)
1451778 paramerters in total
0	0.07093616	0.07069821	0.00060	6.01
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.40 seconds
Model Performance:
- Correlation: -0.08754
- Root Mean Squared Error: 1.80 mm
- Peak Error: 84.328 %
D:\anaconda3\envs\torch22\lib\site-packages\torch\optim\lr_scheduler.py:143: UserWarning: Detected call of `lr_scheduler.step()` before `optimizer.step()`. In PyTorch 1.1.0 and later, you should call them in the opposite order: `optimizer.step()` before `lr_scheduler.step()`.  Failure to do this will result in PyTorch skipping the first value of the learning rate schedule. See more details at https://pytorch.org/docs/stable/optim.html#how-to-adjust-learning-rate
  warnings.warn("Detected call of `lr_scheduler.step()` before `optimizer.step()`. "
1	0.00108330	0.00088555	0.00060	38.16
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 22.63 seconds
Model Performance:
- Correlation: 0.22580
- Root Mean Squared Error: 1.81 mm
- Peak Error: 77.563 %
2	0.00068898	0.00078278	0.00059	77.98
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.91 seconds
Model Performance:
- Correlation: 0.29033
- Root Mean Squared Error: 1.93 mm
- Peak Error: 60.025 %
3	0.00040516	0.00050993	0.00059	113.76
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.23 seconds
Model Performance:
- Correlation: 0.33328
- Root Mean Squared Error: 2.44 mm
- Peak Error: 48.178 %
4	0.00029059	0.00052286	0.00059	148.56
5	0.00023177	0.00034639	0.00059	165.32
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 22.05 seconds
Model Performance:
- Correlation: 0.64597
- Root Mean Squared Error: 1.56 mm
- Peak Error: 36.982 %
6	0.00063238	0.00067985	0.00058	203.11
7	0.00026936	0.00032333	0.00058	216.90
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.10 seconds
Model Performance:
- Correlation: 0.63478
- Root Mean Squared Error: 1.60 mm
- Peak Error: 34.665 %
8	0.00077927	0.00048178	0.00058	250.92
9	0.00069475	0.00040288	0.00057	267.22
10	0.00048220	0.00058657	0.00057	282.34
11	0.00027817	0.00045684	0.00057	296.42
12	0.00034300	0.00026905	0.00057	311.28
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.68 seconds
Model Performance:
- Correlation: 0.67580
- Root Mean Squared Error: 2.33 mm
- Peak Error: 41.495 %
13	0.00031356	0.00040214	0.00056	345.58
14	0.00013538	0.00024239	0.00056	360.49
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.99 seconds
Model Performance:
- Correlation: 0.72784
- Root Mean Squared Error: 2.10 mm
- Peak Error: 35.621 %
15	0.00043275	0.00024526	0.00056	393.03
16	0.00021917	0.00022768	0.00056	407.30
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.30 seconds
Model Performance:
- Correlation: 0.73595
- Root Mean Squared Error: 1.67 mm
- Peak Error: 34.822 %
17	0.00011953	0.00021029	0.00055	439.45
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.99 seconds
Model Performance:
- Correlation: 0.77754
- Root Mean Squared Error: 1.49 mm
- Peak Error: 33.936 %
18	0.00037292	0.00022635	0.00055	473.01
19	0.00013721	0.00025459	0.00055	487.83
20	0.00088539	0.00034294	0.00055	502.55
21	0.00015337	0.00029717	0.00054	517.30
22	0.00017834	0.00024275	0.00054	531.97
23	0.00045565	0.00050148	0.00054	546.82
24	0.00014618	0.00025991	0.00054	561.91
25	0.00028872	0.00029411	0.00053	575.93
26	0.00018537	0.00029089	0.00053	590.74
27	0.00019636	0.00031850	0.00053	605.07
28	0.00019171	0.00024946	0.00053	619.36
29	0.00017009	0.00028556	0.00052	633.98
30	0.00007492	0.00023343	0.00052	648.80
31	0.00032907	0.00033287	0.00052	663.53
32	0.00032707	0.00036609	0.00052	678.10
33	0.00035164	0.00030805	0.00052	692.76
34	0.00015811	0.00026372	0.00051	707.35
35	0.00024211	0.00019784	0.00051	721.47
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.06 seconds
Model Performance:
- Correlation: 0.37812
- Root Mean Squared Error: 7.15 mm
- Peak Error: 211.971 %
36	0.00039711	0.00042906	0.00051	755.29
37	0.00011182	0.00026434	0.00051	770.02
38	0.00017284	0.00027990	0.00050	784.69
39	0.00008497	0.00020452	0.00050	799.02
40	0.00008722	0.00024586	0.00050	813.21
41	0.00020393	0.00026968	0.00050	827.86
42	0.00011707	0.00022771	0.00050	842.55
43	0.00006029	0.00028572	0.00049	857.08
44	0.00046646	0.00016909	0.00049	871.23
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.89 seconds
Model Performance:
- Correlation: 0.63503
- Root Mean Squared Error: 3.18 mm
- Peak Error: 65.523 %
45	0.00045548	0.00024657	0.00049	904.31
46	0.00015315	0.00024944	0.00049	918.86
47	0.00026202	0.00042987	0.00049	933.02
48	0.00015509	0.00014066	0.00048	947.72
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.88 seconds
Model Performance:
- Correlation: 0.88267
- Root Mean Squared Error: 1.23 mm
- Peak Error: 30.480 %
49	0.00007889	0.00017365	0.00048	981.58
50	0.00017717	0.00022140	0.00048	996.54
51	0.00012474	0.00019322	0.00048	1011.38
52	0.00017216	0.00026965	0.00048	1025.87
53	0.00043341	0.00015628	0.00047	1040.22
54	0.00030494	0.00024049	0.00047	1054.29
55	0.00012900	0.00023971	0.00047	1068.71
56	0.00018531	0.00018061	0.00047	1083.05
57	0.00006997	0.00018148	0.00047	1097.35
58	0.00005752	0.00022272	0.00047	1111.33
59	0.00014766	0.00017527	0.00046	1125.45
60	0.00013959	0.00015500	0.00046	1140.09
61	0.00011398	0.00015621	0.00046	1154.10
62	0.00017013	0.00016835	0.00046	1168.54
63	0.00009357	0.00017374	0.00046	1182.74
64	0.00015114	0.00019201	0.00045	1197.30
65	0.00016942	0.00018857	0.00045	1211.86
66	0.00030565	0.00027287	0.00045	1226.50
67	0.00016350	0.00021680	0.00045	1240.68
68	0.00008887	0.00019750	0.00045	1254.71
69	0.00046009	0.00015478	0.00045	1269.18
70	0.00024202	0.00021666	0.00044	1284.61
71	0.00028222	0.00025720	0.00044	1298.84
72	0.00025234	0.00016449	0.00044	1314.87
73	0.00028983	0.00018076	0.00044	1329.65
