Shape of input  per step: torch.Size([120, 5])
Shape of output per step: torch.Size([120, 2])
Dataset length: 417330
Shape of input  per step: torch.Size([120, 5])
Shape of output per step: torch.Size([120, 2])
Dataset length: 395199
TrainingRNN(
  (head): SymmetrizedBlock(
    (rnn): GRU(4, 256, batch_first=True)
  )
  (hidden): ModuleList(
    (0-2): 3 x SymmetrizedBlock(
      (rnn): GRU(256, 256, batch_first=True)
    )
  )
  (fc): Sequential(
    (0): Linear(in_features=256, out_features=256, bias=False)
    (1): Tanh()
    (2): Linear(in_features=256, out_features=2, bias=False)
  )
)
1451520 paramerters in total
0	0.00566369	0.00586059	0.00100	7.95
Using symmetric recursive prediction...
Loading single model from epoch best...
Traceback (most recent call last):
  File "E:\Codes\recLSTM-bridge\train.py", line 161, in <module>
    run(args)
  File "E:\Codes\recLSTM-bridge\train.py", line 98, in run
    rec_results = run_prediction(
  File "E:\Codes\recLSTM-bridge\test.py", line 173, in run_prediction
    model.load_state_dict(update_state_dict(state_dict))
  File "D:\anaconda3\envs\torch22\lib\site-packages\torch\nn\modules\module.py", line 2153, in load_state_dict
    raise RuntimeError('Error(s) in loading state_dict for {}:\n\t{}'.format(
RuntimeError: Error(s) in loading state_dict for InferenceLSTM:
	Missing key(s) in state_dict: "rnns.0.rnn.weight_ih_l0", "rnns.0.rnn.weight_hh_l0", "rnns.0.rnn.bias_ih_l0", "rnns.0.rnn.bias_hh_l0", "rnns.1.rnn.weight_ih_l0", "rnns.1.rnn.weight_hh_l0", "rnns.1.rnn.bias_ih_l0", "rnns.1.rnn.bias_hh_l0", "rnns.2.rnn.weight_ih_l0", "rnns.2.rnn.weight_hh_l0", "rnns.2.rnn.bias_ih_l0", "rnns.2.rnn.bias_hh_l0", "rnns.3.rnn.weight_ih_l0", "rnns.3.rnn.weight_hh_l0", "rnns.3.rnn.bias_ih_l0", "rnns.3.rnn.bias_hh_l0".
	Unexpected key(s) in state_dict: "rnns.0.weight_hh_l0", "rnns.0.weight_ih_l0", "rnns.0.bias_hh_l0", "rnns.0.bias_ih_l0", "rnns.1.weight_hh_l0", "rnns.1.weight_ih_l0", "rnns.1.bias_hh_l0", "rnns.1.bias_ih_l0", "rnns.2.weight_hh_l0", "rnns.2.weight_ih_l0", "rnns.2.bias_hh_l0", "rnns.2.bias_ih_l0", "rnns.3.weight_hh_l0", "rnns.3.weight_ih_l0", "rnns.3.bias_hh_l0", "rnns.3.bias_ih_l0".
