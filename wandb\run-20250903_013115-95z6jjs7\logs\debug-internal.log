{"time":"2025-09-03T01:31:15.5346536+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-09-03T01:31:16.931744+08:00","level":"INFO","msg":"stream: created new stream","id":"95z6jjs7"}
{"time":"2025-09-03T01:31:16.931744+08:00","level":"INFO","msg":"stream: started","id":"95z6jjs7"}
{"time":"2025-09-03T01:31:16.931744+08:00","level":"INFO","msg":"handler: started","stream_id":"95z6jjs7"}
{"time":"2025-09-03T01:31:16.931744+08:00","level":"INFO","msg":"sender: started","stream_id":"95z6jjs7"}
{"time":"2025-09-03T01:31:16.931744+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"95z6jjs7"}
{"time":"2025-09-03T01:31:17.4587492+08:00","level":"ERROR","msg":"git repo not found","error":"repository does not exist"}
{"time":"2025-09-03T06:46:48.4634977+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/95z6jjs7/file_stream\": EOF"}
{"time":"2025-09-03T07:58:44.3486973+08:00","level":"INFO","msg":"handler: operation stats","stats":{"operations":[{"desc":"uploading output.log","runtime_seconds":0.585474},{"desc":"uploading wandb-summary.json","runtime_seconds":0.585474},{"desc":"uploading config.yaml","runtime_seconds":0.1907961}],"total_operations":3}}
{"time":"2025-09-03T07:58:45.6492285+08:00","level":"INFO","msg":"fileTransfer: Close: file transfer manager closed"}
{"time":"2025-09-03T07:58:46.582178+08:00","level":"INFO","msg":"stream: closing","id":"95z6jjs7"}
{"time":"2025-09-03T07:58:46.582178+08:00","level":"INFO","msg":"handler: closed","stream_id":"95z6jjs7"}
{"time":"2025-09-03T07:58:46.582178+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"95z6jjs7"}
{"time":"2025-09-03T07:58:46.582178+08:00","level":"INFO","msg":"sender: closed","stream_id":"95z6jjs7"}
{"time":"2025-09-03T07:58:46.582178+08:00","level":"INFO","msg":"stream: closed","id":"95z6jjs7"}
