import numpy as np
import torch
from utils_model import TrainingRNN
from parse import parse_args

def test_symmetry():
    """Test that the symmetry constraint f(x) = -f(-x') is satisfied"""
    
    # Setup
    args = parse_args()
    net = TrainingRNN(args, True).eval()
    
    # Create test data
    a = np.random.rand(2, 10, 1)
    d = np.random.rand(2, 10, 2)
    
    # Test with non-zero hidden state
    h = torch.Tensor(np.random.rand(1, 2, 256))
    
    # Create input and transformed input
    inp0 = np.concatenate((a, d), -1)
    inp1 = np.concatenate((-a, -d[..., 1:], -d[..., :1]), -1)
    
    # Forward pass
    with torch.no_grad():
        y0 = net(torch.Tensor(inp0), h)
        y1 = net(torch.Tensor(inp1), -h)
    
    print("Testing symmetry constraint f(x) = -f(-x')")
    print(f"Input shape: {inp0.shape}")
    print(f"Output shape: {y0.shape}")
    print(f"Hidden state shape: {h.shape}")
    
    # Check symmetry constraint
    print("\nSymmetry check:")
    print("y0[:,:,0] + y1[:,:,0] (should be close to 0):")
    print(y0[:, :, 0] + y1[:, :, 0])
    print("Max absolute error:", torch.max(torch.abs(y0[:, :, 0] + y1[:, :, 0])).item())
    
    print("\ny1[:,:,1] + y0[:,:,1] (should be close to 0):")
    print(y1[:, :, 1] + y0[:, :, 1])
    print("Max absolute error:", torch.max(torch.abs(y1[:, :, 1] + y0[:, :, 1])).item())
    
    # Test with zero hidden state (should work perfectly)
    print("\n" + "="*50)
    print("Testing with zero hidden state:")
    h_zero = torch.zeros_like(h)
    
    with torch.no_grad():
        y0_zero = net(torch.Tensor(inp0), h_zero)
        y1_zero = net(torch.Tensor(inp1), -h_zero)
    
    print("y0[:,:,0] + y1[:,:,0] (should be exactly 0):")
    print(y0_zero[:, :, 0] + y1_zero[:, :, 0])
    print("Max absolute error:", torch.max(torch.abs(y0_zero[:, :, 0] + y1_zero[:, :, 0])).item())
    
    print("\ny1[:,:,1] + y0[:,:,1] (should be exactly 0):")
    print(y1_zero[:, :, 1] + y0_zero[:, :, 1])
    print("Max absolute error:", torch.max(torch.abs(y1_zero[:, :, 1] + y0_zero[:, :, 1])).item())

if __name__ == "__main__":
    test_symmetry()