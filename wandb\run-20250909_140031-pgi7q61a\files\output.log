Shape of input  per step: torch.<PERSON>ze([148, 5])
Shape of output per step: torch.<PERSON>ze([148, 2])
Dataset length: 846360
Shape of input  per step: torch.Size([148, 5])
Shape of output per step: torch.Size([148, 2])
Dataset length: 396954
TrainingRNN(
  (head): SymmetrizedBlock(
    (rnn): GRU(4, 256, batch_first=True)
  )
  (hidden): ModuleList(
    (0-2): 3 x SymmetrizedBlock(
      (rnn): GRU(256, 256, batch_first=True)
    )
  )
  (fc): Sequential(
    (0): Linear(in_features=256, out_features=256, bias=False)
    (1): Tanh()
    (2): Linear(in_features=256, out_features=2, bias=False)
  )
)
1451520 paramerters in total
0	0.06786974	0.03651868	0.00060	8.49
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.09 seconds
Model Performance:
- Correlation: -0.00332
- Root Mean Squared Error: 2349.52 mm
- Peak Error: 20039.706 %
1	0.00317645	0.00355662	0.00060	47.75
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.26 seconds
Model Performance:
- Correlation: 0.00245
- Root Mean Squared Error: 36838.26 mm
- Peak Error: 271415.521 %
2	0.00063547	0.00134416	0.00059	87.30
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.30 seconds
Model Performance:
- Correlation: 0.00242
- Root Mean Squared Error: 100796.45 mm
- Peak Error: 708465.217 %
3	0.00054375	0.00122991	0.00059	128.63
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.41 seconds
Model Performance:
- Correlation: 0.00246
- Root Mean Squared Error: 140652.35 mm
- Peak Error: 973041.436 %
4	0.00055437	0.00100122	0.00059	169.05
Using symmetric recursive prediction...
Loading single model from epoch best...
