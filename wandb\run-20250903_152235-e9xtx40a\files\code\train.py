import torch
import wandb
import numpy as np
import joblib
from adan import Adan
from parse import parse_args
from utils_model import TrainingLSTM
from utils_data import get_dataset
from utils_data import cal_slice_params
from test import run_prediction
from torch.utils.data import DataLoader
torch.backends.cudnn.benchmark = True


args = parse_args()
if args.auto_slice:
    args = cal_slice_params(args)

res = args.response
idx = args.bridge_idx
wandb.init(project='recLSTM-bridge', tags=[f'b{idx}', res], group=f'b{idx}', config=args)
args.save_path = wandb.run.dir[wandb.run.dir.find('wandb'):]

dtra, dval = get_dataset(args)
dtes, _ = get_dataset(args, train=False)
joblib.dump(args, args.save_path + '/configs.save')


def step_decay(step):
    lr = 1 / (1 + args.decay * int(step/100))
    if (args.initial_lr*lr)<5e-5:
        lr = 5e-5 / args.initial_lr
    return lr


def run(args):
    import time
    ds_tra = DataLoader(dtra, batch_size=args.batch_size, shuffle=True, pin_memory=False, num_workers=0)
    ds_val = DataLoader(dval, batch_size=args.batch_size, shuffle=True, pin_memory=False, num_workers=0)
    ds_tes = DataLoader(dtes, batch_size=args.batch_size, shuffle=True, pin_memory=False, num_workers=0)
    
    net = TrainingLSTM(args).cuda()
    # net = torch.compile(net)
    
    print(net)
    print("{} paramerters in total".format(sum(c.numel() for c in net.parameters())))
    
    criterion = torch.nn.MSELoss().cuda()
    if args.adan:
        optimizer = Adan(
            net.parameters(),
            lr=args.initial_lr,
            weight_decay=args.weight_decay,
            betas=[0.98, 0.92, 0.99],
            eps = args.opt_eps,
            max_grad_norm=args.max_grad_norm,
            no_prox=args.no_prox
            )
    else:
        optimizer = torch.optim.Adam(net.parameters(), lr=args.initial_lr, weight_decay=0.0)
    scheduler = torch.optim.lr_scheduler.LambdaLR(optimizer, step_decay, last_epoch=-1)
    
    use_amp = args.use_amp
    scaler = torch.cuda.amp.GradScaler(enabled=use_amp)
    if args.resume != 'none':
        # example: 'wandb/run-20221030_142149-ro19p74v/files'
        net.load_state_dict(torch.load(args.resume + '/best.pkl'))
        optimizer.load_state_dict(torch.load(args.resume + '/opt.pkl'))
    
    t0 = time.time()
    vloss_min = 1e+8
    epoch_best = 0
    # train_steps_per_epoch = 100
    
    # try:
    for t in range(args.epochs*100):
        net.train()
        # t1 = time.time()
        x, ytrue = next(iter(ds_tra))
        # print(time.time() - t1)
        with torch.cuda.amp.autocast(enabled=use_amp):
            x, ytrue = x.cuda(), ytrue.cuda()
            y = net(x)
            loss = calculate_loss(x, y, ytrue, criterion, args)
            
        if t % 100 == 0:
            vloss = val(net, ds_val, criterion, args)
            _     = val(net, ds_tes, criterion, args, test=True)
            print('{}\t{:.8f}\t{:.8f}\t{:.5f}\t{:.2f}'.format(t//100, loss.item(), vloss, scheduler.get_last_lr()[0], time.time()-t0))
            
            if vloss_min > vloss:
                vloss_min = vloss
                epoch_best = t//100
                torch.save(net.state_dict(), args.save_path + '/best.pkl')
                if t//100 > 5:
                    torch.save(net.state_dict(), args.save_path + '/{}.pkl'.format(t//100))
                
                rec_results = run_prediction(
                    args.save_path, data_path=args.load_path,
                    past_history=args.past_history, tta=False, plot=False
                )
                wandb.log({'rec-r': rec_results[0]}, commit=False)
                wandb.log({'rec-rmse': rec_results[1]*1000}, commit=False)
                wandb.log({'rec-pe': rec_results[2]*100}, commit=False)
                
                torch.save(optimizer.state_dict(), args.save_path + '/opt.pkl')
                wandb.run.summary['best-vloss'] = vloss_min
                wandb.run.summary['best-epoch'] = epoch_best
                
            wandb.log({'epoch': t//100, 'loss': loss}, commit=True)
        
        scaler.scale(loss).backward()
        scaler.step(optimizer)
        scaler.update()
        optimizer.zero_grad()
        scheduler.step()
    # except (Exception, KeyboardInterrupt) as e:
    #     print(f"Run interrupted by: {e}")
    # finally:
    wandb.finish()
        
    torch.save(net.state_dict(), args.save_path + '/final.pkl')
    print('Best loss: {:.9f} at epoch {}'.format(vloss_min, epoch_best))
    # wandb.finish()
    return net


def val(net, ds, criterion, args, test=False):
    net.eval()
    loss = []
    with torch.no_grad():
        # for i, (x, ytrue) in enumerate(ds):
        for i in range(50):
            x, ytrue = next(iter(ds))
            x, ytrue = x.cuda(), ytrue.cuda()
            y = net(x)
            loss0 = calculate_loss(x, y, ytrue, criterion, args)
            loss.append(loss0.cpu())
    loss = np.mean(loss)
    if not test:
        wandb.log({'val_loss': loss}, commit=False)
    else:
        wandb.log({'tes_loss': loss}, commit=False)
    net.train()
    return loss


def calculate_loss(x, ypred, ytrue, criterion, args):
    '''Weighted MSE Loss on cumulative displacement'''
    inc_window_true = ytrue[:, -args.loss_length:]
    inc_window_pred = ypred[:, -args.loss_length:]
    
    dis_window_pred = torch.cumsum(inc_window_pred, dim=1)
    dis_window_true = torch.cumsum(inc_window_true, dim=1)
    
    loss = torch.mean((dis_window_pred - dis_window_true)**2)
    return loss
    

if __name__ == '__main__':
    run(args)
