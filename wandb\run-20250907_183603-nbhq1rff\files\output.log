Shape of input  per step: torch.Size([120, 5])
Shape of output per step: torch.Size([120, 2])
Dataset length: 834660
Shape of input  per step: torch.Size([120, 5])
Shape of output per step: torch.Size([120, 2])
Dataset length: 395199
TrainingRNN(
  (head): GRU(4, 256, batch_first=True)
  (hidden): ModuleList(
    (0-2): 3 x GRU(256, 256, batch_first=True)
  )
  (fc): Sequential(
    (0): Linear(in_features=256, out_features=256, bias=True)
    (1): Tanh()
    (2): Linear(in_features=256, out_features=2, bias=True)
  )
)
1451778 paramerters in total
0	2.25817299	2.25834680	0.00060	4.52
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 14.67 seconds
Model Performance:
- Correlation: 0.13216
- Root Mean Squared Error: 1.73 mm
- Peak Error: 86.700 %
D:\anaconda3\envs\torch22\lib\site-packages\torch\optim\lr_scheduler.py:143: UserWarning: Detected call of `lr_scheduler.step()` before `optimizer.step()`. In PyTorch 1.1.0 and later, you should call them in the opposite order: `optimizer.step()` before `lr_scheduler.step()`.  Failure to do this will result in PyTorch skipping the first value of the learning rate schedule. See more details at https://pytorch.org/docs/stable/optim.html#how-to-adjust-learning-rate
  warnings.warn("Detected call of `lr_scheduler.step()` before `optimizer.step()`. "
1	0.00100016	0.00106897	0.00060	31.03
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 14.74 seconds
Model Performance:
- Correlation: 0.23316
- Root Mean Squared Error: 1.89 mm
- Peak Error: 76.614 %
2	0.00097127	0.00095165	0.00059	57.62
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 14.60 seconds
Model Performance:
- Correlation: 0.32420
- Root Mean Squared Error: 2.01 mm
- Peak Error: 58.153 %
3	0.00048193	0.00073854	0.00059	84.07
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 14.56 seconds
Model Performance:
- Correlation: 0.46202
- Root Mean Squared Error: 2.00 mm
- Peak Error: 41.967 %
4	0.00053150	0.00081506	0.00059	110.48
5	0.00040397	0.00060635	0.00059	121.76
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 14.71 seconds
Model Performance:
- Correlation: 0.59813
- Root Mean Squared Error: 1.67 mm
- Peak Error: 40.873 %
6	0.00074719	0.00077447	0.00058	148.73
7	0.00041351	0.00054377	0.00058	160.02
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 15.26 seconds
Model Performance:
- Correlation: 0.59828
- Root Mean Squared Error: 1.84 mm
- Peak Error: 30.904 %
8	0.00035659	0.00051091	0.00058	187.21
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 14.96 seconds
Model Performance:
- Correlation: 0.55502
- Root Mean Squared Error: 2.28 mm
- Peak Error: 36.624 %
9	0.00026227	0.00043882	0.00057	214.08
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 15.00 seconds
Model Performance:
- Correlation: 0.50536
- Root Mean Squared Error: 3.28 mm
- Peak Error: 63.713 %
10	0.00009700	0.00022449	0.00057	241.31
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 15.09 seconds
Model Performance:
- Correlation: 0.77425
- Root Mean Squared Error: 1.31 mm
- Peak Error: 21.251 %
11	0.00072036	0.00031574	0.00057	268.35
12	0.00038355	0.00057764	0.00057	279.80
13	0.00084833	0.00034593	0.00056	291.18
14	0.00022479	0.00039811	0.00056	302.54
15	0.00068765	0.00069986	0.00056	313.80
16	0.00070298	0.00086183	0.00056	325.10
17	0.00019846	0.00035515	0.00055	336.47
18	0.00007186	0.00019301	0.00055	347.74
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 14.58 seconds
Model Performance:
- Correlation: 0.78041
- Root Mean Squared Error: 1.35 mm
- Peak Error: 21.045 %
19	0.00047615	0.00028939	0.00055	374.16
20	0.00012492	0.00023356	0.00055	385.48
21	0.00022160	0.00039594	0.00054	396.83
22	0.00019552	0.00029939	0.00054	408.20
23	0.00043049	0.00021977	0.00054	419.47
24	0.00042906	0.00050573	0.00054	430.80
25	0.00011547	0.00015672	0.00053	442.12
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 14.61 seconds
Model Performance:
- Correlation: 0.79960
- Root Mean Squared Error: 1.30 mm
- Peak Error: 20.753 %
26	0.00009297	0.00019184	0.00053	468.62
27	0.00025225	0.00029078	0.00053	480.35
28	0.00018323	0.00028874	0.00053	491.80
29	0.00013121	0.00024713	0.00052	503.74
30	0.00005158	0.00015170	0.00052	515.79
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.85 seconds
Model Performance:
- Correlation: 0.80761
- Root Mean Squared Error: 1.37 mm
- Peak Error: 20.298 %
31	0.00017058	0.00020320	0.00052	546.11
32	0.00020530	0.00015322	0.00052	560.20
33	0.00007694	0.00017888	0.00052	574.89
34	0.00010253	0.00018225	0.00051	588.13
35	0.00009654	0.00015469	0.00051	600.32
36	0.00011434	0.00020722	0.00051	613.41
37	0.00006122	0.00019353	0.00051	625.84
38	0.00023256	0.00029334	0.00050	638.89
39	0.00009239	0.00022404	0.00050	651.69
40	0.00015090	0.00022841	0.00050	664.08
41	0.00006855	0.00017522	0.00050	676.52
42	0.00010817	0.00016829	0.00050	688.55
43	0.00008170	0.00016596	0.00049	700.07
44	0.00008895	0.00021605	0.00049	711.82
45	0.00014265	0.00018287	0.00049	723.13
46	0.00038199	0.00020251	0.00049	734.49
47	0.00005481	0.00017397	0.00049	745.84
48	0.00009916	0.00018619	0.00048	757.11
49	0.00015221	0.00019578	0.00048	768.39
50	0.00024716	0.00027804	0.00048	779.75
51	0.00008580	0.00015699	0.00048	791.07
52	0.00053587	0.00016610	0.00048	802.44
53	0.00006234	0.00013024	0.00047	813.72
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 14.75 seconds
Model Performance:
- Correlation: 0.82500
- Root Mean Squared Error: 1.32 mm
- Peak Error: 12.922 %
54	0.00005289	0.00013596	0.00047	840.61
55	0.00005987	0.00015536	0.00047	852.45
56	0.00003811	0.00011290	0.00047	866.16
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 25.51 seconds
Model Performance:
- Correlation: 0.73571
- Root Mean Squared Error: 1.75 mm
- Peak Error: 15.807 %
57	0.00025162	0.00026442	0.00047	903.91
58	0.00010885	0.00017996	0.00047	915.65
59	0.00011426	0.00027868	0.00046	927.34
60	0.00014333	0.00011751	0.00046	939.16
61	0.00005679	0.00016569	0.00046	951.41
62	0.00036919	0.00017928	0.00046	963.39
63	0.00006507	0.00012680	0.00046	974.69
64	0.00012761	0.00013466	0.00045	986.10
65	0.00005728	0.00020271	0.00045	997.29
66	0.00025654	0.00014145	0.00045	1008.50
67	0.00009893	0.00015969	0.00045	1019.74
68	0.00022347	0.00017872	0.00045	1030.99
69	0.00003189	0.00010946	0.00045	1042.62
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 14.56 seconds
Model Performance:
- Correlation: 0.79973
- Root Mean Squared Error: 1.69 mm
- Peak Error: 13.896 %
70	0.00003820	0.00010752	0.00044	1068.99
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 14.49 seconds
Model Performance:
- Correlation: 0.84899
- Root Mean Squared Error: 1.26 mm
- Peak Error: 18.219 %
71	0.00005643	0.00010541	0.00044	1095.32
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 14.51 seconds
Model Performance:
- Correlation: 0.61263
- Root Mean Squared Error: 3.16 mm
- Peak Error: 42.308 %
72	0.00004889	0.00012157	0.00044	1121.66
73	0.00007063	0.00010595	0.00044	1133.02
74	0.00005044	0.00011258	0.00044	1144.64
75	0.00005925	0.00014134	0.00044	1155.83
76	0.00017932	0.00009655	0.00043	1167.13
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 14.63 seconds
Model Performance:
- Correlation: 0.82449
- Root Mean Squared Error: 1.38 mm
- Peak Error: 13.353 %
77	0.00011314	0.00012363	0.00043	1193.75
78	0.00007682	0.00014866	0.00043	1205.08
79	0.00009470	0.00009530	0.00043	1216.34
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 15.35 seconds
Model Performance:
- Correlation: 0.84852
- Root Mean Squared Error: 1.26 mm
- Peak Error: 13.439 %
80	0.00015680	0.00010257	0.00043	1243.79
81	0.00006604	0.00009151	0.00043	1256.32
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.37 seconds
Model Performance:
- Correlation: 0.85517
- Root Mean Squared Error: 1.27 mm
- Peak Error: 14.435 %
82	0.00019779	0.00017773	0.00043	1288.91
83	0.00025069	0.00009925	0.00042	1300.57
84	0.00006571	0.00008600	0.00042	1312.07
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 14.84 seconds
Model Performance:
- Correlation: 0.78165
- Root Mean Squared Error: 1.57 mm
- Peak Error: 13.742 %
85	0.00004608	0.00012147	0.00042	1339.10
86	0.00011114	0.00018290	0.00042	1350.75
87	0.00004522	0.00012984	0.00042	1362.16
88	0.00015289	0.00009032	0.00042	1373.44
89	0.00005088	0.00011737	0.00042	1384.89
90	0.00024406	0.00010126	0.00041	1396.42
91	0.00007613	0.00009278	0.00041	1408.21
92	0.00006665	0.00013566	0.00041	1419.43
93	0.00005928	0.00010487	0.00041	1430.62
94	0.00006205	0.00011151	0.00041	1441.88
95	0.00013386	0.00015940	0.00041	1453.09
96	0.00004851	0.00009691	0.00041	1464.37
97	0.00007777	0.00011583	0.00040	1475.53
98	0.00007334	0.00011224	0.00040	1486.70
99	0.00003907	0.00011802	0.00040	1498.04
100	0.00008352	0.00008745	0.00040	1509.38
101	0.00029765	0.00007059	0.00040	1520.58
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 14.71 seconds
Model Performance:
- Correlation: 0.86383
- Root Mean Squared Error: 1.30 mm
- Peak Error: 12.195 %
102	0.00009454	0.00008453	0.00040	1547.15
103	0.00002686	0.00007330	0.00040	1558.48
104	0.00015520	0.00008008	0.00039	1569.83
105	0.00003308	0.00010001	0.00039	1581.10
106	0.00004310	0.00007416	0.00039	1592.38
107	0.00010637	0.00011487	0.00039	1603.70
108	0.00007233	0.00010241	0.00039	1615.05
109	0.00015471	0.00009669	0.00039	1626.38
110	0.00007341	0.00013713	0.00039	1637.64
111	0.00009998	0.00009629	0.00039	1648.92
112	0.00004851	0.00010907	0.00038	1660.24
113	0.00010907	0.00010497	0.00038	1671.55
114	0.00003779	0.00008219	0.00038	1682.79
115	0.00004482	0.00009278	0.00038	1694.02
116	0.00003395	0.00007392	0.00038	1706.38
117	0.00001766	0.00007541	0.00038	1719.06
118	0.00003295	0.00009244	0.00038	1731.67
119	0.00012261	0.00006005	0.00038	1743.62
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.76 seconds
Model Performance:
- Correlation: 0.87434
- Root Mean Squared Error: 1.18 mm
- Peak Error: 10.746 %
120	0.00004961	0.00007475	0.00037	1774.84
121	0.00009566	0.00012166	0.00037	1786.95
122	0.00007166	0.00011042	0.00037	1798.87
123	0.00003640	0.00005398	0.00037	1810.98
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.61 seconds
Model Performance:
- Correlation: 0.86643
- Root Mean Squared Error: 1.34 mm
- Peak Error: 10.512 %
124	0.00011170	0.00015955	0.00037	1840.67
125	0.00006472	0.00006775	0.00037	1854.92
126	0.00006883	0.00010564	0.00037	1870.22
127	0.00004756	0.00006662	0.00037	1885.27
128	0.00004668	0.00006830	0.00037	1900.82
129	0.00005314	0.00007750	0.00036	1914.09
130	0.00019759	0.00007963	0.00036	1926.15
131	0.00004493	0.00007570	0.00036	1938.93
132	0.00003594	0.00007567	0.00036	1950.77
133	0.00003870	0.00005473	0.00036	1962.61
134	0.00003973	0.00006670	0.00036	1974.39
135	0.00005207	0.00008069	0.00036	1985.90
136	0.00003714	0.00004567	0.00036	1998.07
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.31 seconds
Model Performance:
- Correlation: 0.84595
- Root Mean Squared Error: 1.43 mm
- Peak Error: 11.187 %
137	0.00003892	0.00007455	0.00036	2027.80
138	0.00004870	0.00007807	0.00036	2039.99
139	0.00004396	0.00006573	0.00035	2051.51
140	0.00002802	0.00005813	0.00035	2063.03
