_wandb:
    value:
        cli_version: 0.21.0
        code_path: code/train.py
        e:
            nf1fuf254mb4i0diak83vq1yxkbh5rwv:
                args:
                    - --bridge_idx=33
                    - --response=bear
                    - --past_history=120
                    - --loss_length=40
                    - --adan
                    - --layers=4
                    - --pred_dim=1
                codePath: train.py
                codePathLocal: train.py
                cpu_count: 14
                cpu_count_logical: 20
                cudaVersion: "12.6"
                disk:
                    /:
                        total: "************"
                        used: "************"
                email: <EMAIL>
                executable: D:\anaconda3\envs\torch22\python.exe
                gpu: NVIDIA GeForce RTX 4060 Ti
                gpu_count: 1
                gpu_nvidia:
                    - architecture: Ada
                      cudaCores: 4352
                      memoryTotal: "17175674880"
                      name: NVIDIA GeForce RTX 4060 Ti
                      uuid: GPU-d0fde4af-f8a8-fdca-3317-74078f49b557
                host: DESKTOP-PB9IBRM
                memory:
                    total: "34188517376"
                os: Windows-10-10.0.26100-SP0
                program: E:\Codes\recLSTM-bridge\train.py
                python: CPython 3.9.18
                root: E:\Codes\recLSTM-bridge
                startedAt: "2025-09-03T09:30:19.594877Z"
                writerId: nf1fuf254mb4i0diak83vq1yxkbh5rwv
        m: []
        python_version: 3.9.18
        t:
            "1":
                - 1
                - 53
            "2":
                - 1
                - 11
                - 49
                - 53
            "3":
                - 2
                - 15
                - 16
                - 62
            "4": 3.9.18
            "5": 0.21.0
            "8":
                - 3
            "12": 0.21.0
            "13": windows-amd64
adan:
    value: true
auto_slice:
    value: false
batch_size:
    value: 512
bridge_idx:
    value: 33
decay:
    value: 0.005
dt:
    value: 0.02
epochs:
    value: 2000
flip:
    value: false
fliplr:
    value: true
global_sample_step:
    value: 1
initial_epoch:
    value: 0
initial_lr:
    value: 0.001
layers:
    value: 4
load_path:
    value: data/
local_sample_step:
    value: 3
loss_length:
    value: 40
max_grad_norm:
    value: 1
no_prox:
    value: false
opt_betas:
    value: null
opt_eps:
    value: 1e-08
past_history:
    value: 120
pred_dim:
    value: 1
response:
    value: bear
resume:
    value: none
save_path:
    value: save/
shift_acc:
    value: false
split:
    value: 0.95
use_amp:
    value: true
weight_decay:
    value: 0.01
width:
    value: 256
window_sliding_step:
    value: 1
zero_init:
    value: false
