import torch
import torch.nn as nn


class SymmetrizedBlock(torch.nn.Module):
    '''
    An RNN block that enforces anti-symmetry by design.

    This module wraps a standard RNN layer (g), such as nn.GRU, to construct a new
    function (f) that is guaranteed to be anti-symmetric with respect to a given
    transformation (T).
    
    Core Idea:
    The output `f(x)` is constructed using the formula:
        f(x) = 0.5 * (g(x) - g(T(x)))
    where `g` is the base RNN and `T` is an involutive transformation (i.e., T(T(x)) = x).

    Mathematical Properties:
    1.  Anti-Symmetry: The constructed function `f` is anti-symmetric with respect
        to the transformation `T`. This means `f(T(x)) = -f(x)`.
        Proof:
            f(T(x)) = 0.5 * (g(T(x)) - g(T(T(x))))
                    = 0.5 * (g(T(x)) - g(x))   (since T is an involution)
                    = -0.5 * (g(x) - g(T(x)))
                    = -f(x)

    2.  Equivariance: This anti-symmetry is a specific instance of equivariance.
        A function `f` is equivariant if `f(T(x)) = T'(f(x))`, where `T` acts on
        the input and `T'` acts on the output. In our case, the output
        transformation `T'` is simple negation: `T'(y) = -y`.
    
    Input Transformation (T):
    The specific transformation `T` depends on the `reverse` flag:
    - If `reverse=True` (for the first layer):
        `T(x) = -x'`, where `x'` is `x` with the latter half of its feature
        dimension reversed. This is designed for the input `(bs, T, 1+C) -> (bs, T, 2*C)`
        where the first `C` features are from one source and the last `C` are from another.
    - If `reverse=False` (for hidden layers):
        `T(x) = -x`. This is a simple negation, as the features in hidden layers
        no longer have the initial distinct structure.

    Parameters:
        c_in (int): Number of input features.
        c_out (int): Number of output features (and hidden size of the GRU).
        reverse (bool): If True, applies the feature-reversing transformation.
                        Otherwise, applies simple negation.
    '''
    def __init__(self, c_in, c_out, reverse=False):
        super().__init__()
        self.reverse = reverse
        self.rnn = nn.GRU(c_in, c_out, 1, bias=True, batch_first=True)
        self.act = torch.tanh
        
    def _transform_input(self, x: torch.Tensor) -> torch.Tensor:
        """Helper function T(x) = -x'"""
        if self.reverse:
            split_point = x.shape[-1] // 2
            a = x[..., :split_point]
            d = x[..., split_point:]
            
            d_reversed = torch.flip(d, dims=[-1])
            x_prime = torch.cat([a, d_reversed], dim=-1)
            return -x_prime
        # For subsequent layers, the transformation is simple negation.
        return -x
    
    def forward(self, x, states=None):
        # Get the original batch size
        batch_size = x.shape[0]
        
        # T(x) = -x'
        x_transformed = self._transform_input(x)
        x_combined = torch.cat([x, x_transformed], dim=0)
        
        y_combined, states = self.rnn(x_combined, states)
        y_combined = self.act(y_combined)
        
        y_pos = y_combined[:batch_size]
        y_neg = y_combined[batch_size:]
        y_out = 0.5 * (y_pos - y_neg)
        
        h_out_combined = states
        return y_out, states
        states = {
            'h_pos': h_out_combined[:, :batch_size],
            'h_neg': h_out_combined[:, batch_size:],
        }
        
        return y_out, states
        


import math
import warnings

# 步骤 1: 创建 Transformer 必需的位置编码模块
class PositionalEncoding(nn.Module):
    def __init__(self, d_model, dropout=0., max_len=5000):
        super(PositionalEncoding, self).__init__()
        
        if d_model % 2 != 0:
            warnings.warn(f"d_model is {d_model} which is odd. This is not recommended for PositionalEncoding.")
            
        self.dropout = nn.Dropout(p=dropout)

        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        cos_term = torch.cos(position * div_term)
        pe[:, 1::2] = cos_term[:, :pe[:, 1::2].shape[1]]
        
        pe = pe.unsqueeze(0) # (1, max_len, d_model)
        self.register_buffer('pe', pe)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        x: Tensor, shape [batch_size, seq_len, d_model]
        """
        x = x + self.pe[:, :x.size(1)]
        return self.dropout(x)

# 步骤 2: 创建替换 LSTM 的 SymmetrizedTransformer
class SymmetrizedTransformer(nn.Module):
    def __init__(self, c_in, c_out, n_head=8, n_layers=3, dropout=0.1):
        """
        c_in: 输入特征维度 (d_model)
        c_out: 输出特征维度
        n_head: 多头注意力机制的头数
        n_layers: Transformer Encoder的层数
        """
        super().__init__()
        
        # TransformerEncoderLayer 定义了单层的结构
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=c_in, 
            nhead=n_head, 
            dropout=dropout,
            batch_first=True,
            dim_feedforward=c_in * 4
        )
        
        # TransformerEncoder 将多个层堆叠起来
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer, 
            num_layers=n_layers
        )
        
        # 位置编码
        self.pos_encoder = PositionalEncoding(d_model=c_in, dropout=dropout)
        self.final_linear = nn.Linear(c_in, c_out)
        self.act = torch.tanh
        
        # 用于生成因果掩码
        self.causal_mask = None

    def _generate_square_subsequent_mask(self, sz, device):
        # 只有当mask未创建或尺寸/设备不匹配时才重新生成
        if self.causal_mask is None or self.causal_mask.size(0) != sz or self.causal_mask.device != device:
            mask = torch.triu(torch.full((sz, sz), float('-inf'), device=device), diagonal=1)
            self.causal_mask = mask
        return self.causal_mask.to(device)

    def forward(self, x):
        """
        输入 x 的 shape: [batch_size, seq_len, c_in]
        输出 y_out 的 shape: [batch_size, seq_len, c_out]
        注意：不再返回 states
        """
        # 1. 保持对称性输入处理
        batch_size = x.shape[0]
        seq_len = x.shape[1]
        x_combined = torch.cat([x, -x], dim=0) # shape: [2*batch_size, seq_len, c_in]

        # 2. Transformer 核心处理
        # 2.1. 添加位置编码
        x_with_pos = self.pos_encoder(x_combined)
        
        # 2.2. 生成因果掩码
        mask = self._generate_square_subsequent_mask(seq_len, x.device)
        
        # mask 的 shape 是 [seq_len, seq_len]
        y_combined = self.transformer_encoder(x_with_pos, mask) # shape: [2*batch_size, seq_len, c_in]
        
        y_combined = self.final_linear(y_combined) # shape: [2*batch_size, seq_len, c_out]
        y_combined = self.act(y_combined)
        
        # 4. 保持对称性输出处理
        y_pos = y_combined[:batch_size]
        y_neg = y_combined[batch_size:]
        y_out = 0.5 * (y_pos - y_neg)
        
        # Transformer 是无状态的，所以不返回 states
        return y_out

