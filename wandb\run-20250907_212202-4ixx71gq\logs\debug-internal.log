{"time":"2025-09-07T21:22:03.1356984+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-09-07T21:22:09.5895253+08:00","level":"INFO","msg":"stream: created new stream","id":"4ixx71gq"}
{"time":"2025-09-07T21:22:09.5895253+08:00","level":"INFO","msg":"stream: started","id":"4ixx71gq"}
{"time":"2025-09-07T21:22:09.5900599+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"4ixx71gq"}
{"time":"2025-09-07T21:22:09.5900599+08:00","level":"INFO","msg":"handler: started","stream_id":"4ixx71gq"}
{"time":"2025-09-07T21:22:09.5900599+08:00","level":"INFO","msg":"sender: started","stream_id":"4ixx71gq"}
{"time":"2025-09-07T21:39:33.846097+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/4ixx71gq/file_stream\": unexpected EOF"}
{"time":"2025-09-07T21:42:03.3242221+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/4ixx71gq/file_stream\": EOF"}
{"time":"2025-09-07T21:53:42.8286303+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/4ixx71gq/file_stream\": EOF"}
{"time":"2025-09-07T21:58:19.6290094+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/4ixx71gq/file_stream\": EOF"}
{"time":"2025-09-07T22:10:40.7800644+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/4ixx71gq/file_stream\": EOF"}
{"time":"2025-09-07T22:13:40.9077367+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/4ixx71gq/file_stream\": EOF"}
{"time":"2025-09-07T22:16:14.7080334+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/4ixx71gq/file_stream\": EOF"}
{"time":"2025-09-07T22:17:11.6392808+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/4ixx71gq/file_stream\": EOF"}
{"time":"2025-09-07T22:19:56.1872572+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/4ixx71gq/file_stream\": EOF"}
{"time":"2025-09-07T22:21:02.9033811+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/4ixx71gq/file_stream\": EOF"}
{"time":"2025-09-07T22:25:17.8114273+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/4ixx71gq/file_stream\": EOF"}
{"time":"2025-09-07T23:03:56.8810012+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/4ixx71gq/file_stream\": unexpected EOF"}
{"time":"2025-09-07T23:06:00.3186662+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/4ixx71gq/file_stream\": unexpected EOF"}
{"time":"2025-09-07T23:10:29.8754623+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/4ixx71gq/file_stream\": EOF"}
{"time":"2025-09-07T23:12:37.5859393+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/4ixx71gq/file_stream\": unexpected EOF"}
{"time":"2025-09-07T23:15:58.8443172+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/4ixx71gq/file_stream\": unexpected EOF"}
{"time":"2025-09-07T23:20:46.7475308+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/4ixx71gq/file_stream\": unexpected EOF"}
{"time":"2025-09-07T23:21:03.2378712+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/4ixx71gq/file_stream\": EOF"}
{"time":"2025-09-07T23:33:59.8556098+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/4ixx71gq/file_stream\": EOF"}
{"time":"2025-09-07T23:35:45.5479633+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/4ixx71gq/file_stream\": unexpected EOF"}
{"time":"2025-09-07T23:43:48.4865361+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/4ixx71gq/file_stream\": EOF"}
{"time":"2025-09-07T23:46:04.9497741+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": unexpected EOF"}
