{"time":"2025-09-03T16:26:46.1372831+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-09-03T16:26:47.6462735+08:00","level":"INFO","msg":"stream: created new stream","id":"rv15adt2"}
{"time":"2025-09-03T16:26:47.6462735+08:00","level":"INFO","msg":"stream: started","id":"rv15adt2"}
{"time":"2025-09-03T16:26:47.6462735+08:00","level":"INFO","msg":"handler: started","stream_id":"rv15adt2"}
{"time":"2025-09-03T16:26:47.6462735+08:00","level":"INFO","msg":"sender: started","stream_id":"rv15adt2"}
{"time":"2025-09-03T16:26:47.6468294+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"rv15adt2"}
{"time":"2025-09-03T16:26:48.08813+08:00","level":"ERROR","msg":"git repo not found","error":"repository does not exist"}
{"time":"2025-09-03T16:43:45.4230581+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/rv15adt2/file_stream\": unexpected EOF"}
{"time":"2025-09-03T17:11:20.0140811+08:00","level":"ERROR","msg":"HTTP error","status":404,"method":"POST","url":"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/rv15adt2/file_stream"}
{"time":"2025-09-03T17:11:20.0146469+08:00","level":"ERROR+4","msg":"filestream: fatal error: filestream: failed to upload: 404 Not Found path=files/xzk8559/recLSTM-bridge/rv15adt2/file_stream: {\"error\":\"run recLSTM-bridge/rv15adt2 not found while streaming file\"}"}
