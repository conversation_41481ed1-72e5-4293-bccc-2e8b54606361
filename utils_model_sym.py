"""
Neural Network Models for Time Series Prediction
===============================================

This module contains neural network models for time series prediction tasks,
particularly focused on structural response prediction.

Two main model types are implemented:
1. TrainingLSTM: A basic LSTM model for training (simpler, used during training phase)
2. InferenceLSTM: An advanced LSTM model for autoregressive prediction (more complex, used during inference)

Author: Your Name
License: MIT
"""

import torch
import torch.nn as nn
import numpy as np
import scipy.io
import joblib

from utils_data import Scaler, lowpass
from SymmetrizedBlock import SymmetrizedBlock


class InferenceLSTM(torch.nn.Module):
    """
    Recurrent Neural Network model for time series prediction (INFERENCE MODEL).
    
    Key differences from TrainingLSTM:
    - Maintains persistent hidden states between prediction steps
    - Implements recursive prediction (uses previous predictions as inputs)
    - Manages multiple parallel "threads" of prediction
    
    Architecture:
        - Input: Combined acceleration and displacement values
        - Multiple stacked LSTM layers with persistent states
        - Output: Displacement predictions for multiple floors
        
    Parameters:
    -----------
    args : object
        Configuration object with the following attributes:
        - width: Hidden dimension size for LSTM cells
        - layers: Number of stacked LSTM layers
        - local_sample_step: Step size for local sampling in autoregressive prediction
        - past_history: Number of past steps to consider for prediction
        - floor: Number of output dimensions (floors in structural modeling)
    """
    def __init__(self, args, tta=False, sym=True):
        super().__init__()
        self.args = args
        self.r_max = 0.2  # estimated with max(inc)/max(abs)
        
        # Model architecture parameters
        self.width = args.width  # Width of hidden layers
        self.layers = args.layers  # Number of LSTM layers
        self.ls = args.local_sample_step  # Local sampling step size
        self.ph = args.past_history  # Past history length
        self.hs = self.ls * self.ph  # Total history size
        self.tta = tta
        self.sym = sym
        
        args.save_path = args.save_path[args.save_path.find('wandb'):]
        self.scaler_path = args.save_path
        
        # State variables
        self.bs = None  # Batch size (set during forward pass)
        self.h, self.c = None, None  # Hidden and cell states
        self.h_neg, self.c_neg = None, None
        
        # Model components
        rnn = SymmetrizedBlock if sym else nn.GRU
        self.pred_dim = args.pred_dim
        self.in_dim = args.pred_dim * 2
        self.out_dim = args.pred_dim
        
        # RNN layers
        if sym:
            self.rnns = nn.ModuleList(
                [rnn(self.in_dim , self.width, reverse=True)] +
                [rnn(self.width, self.width, reverse=False) for i in range(self.layers-1)]
            )
        else:
            self.rnns = nn.ModuleList(
                [rnn(self.in_dim , self.width, 1, bias=True, batch_first=True)] +
                [rnn(self.width, self.width, 1, bias=True, batch_first=True) for i in range(self.layers-1)]
            )
        # Output layers
        final_hidden_dim = args.width
        self.fc = nn.Sequential(
            nn.Linear(final_hidden_dim, args.width, bias=False if sym else True),
            nn.Tanh(),
            nn.Linear(args.width, self.out_dim, bias=False if sym else True),
        )
        
        self.eval()

    def forward(self, data_path):
        """
        Forward pass through the model for autoregressive prediction.
        
        This method performs the full prediction pipeline:
        1. Pre-processes the input data from the specified path
        2. Resets the LSTM hidden and cell states
        3. Performs step-by-step autoregressive prediction
        4. Post-processes the results
        
        Parameters:
        -----------
        data_path : str
            Path to test data directory
            
        Returns:
        --------
        tuple
            (predictions, ground_truth, sequence_lengths)
        """
        import time
        with torch.no_grad():
            # 1. Pre-process data
            history, ground_truth, sequence_lengths = self.pre_process(data_path)
            abs = torch.zeros(*history.shape)[..., 1:].cuda()
            
            # 2. Autoregressive prediction loop
            tick1 = time.time()
            
            # initialize hidden states
            # self.init_states(history, self.hs, self.ls)
            self.reset_hidden_states()
            # For each time step beyond history window
            for t in range(self.hs, self.hs + ground_truth.shape[1]):
                # Select thread based on time step
                thread = (t - self.hs) % self.ls
                # Get input window for current step
                t_range = np.arange(t-self.ls, t-self.ls+1)
                window = history[:, t_range]
                window = torch.cat([window[...,:1], abs[:,t_range]], -1)
                # Predict one step
                if self.tta:
                    # Predict output in the first pass
                    history[:, t, 1:] = self.forward_once_in_loop(window, thread, update_h=False)[:, -1]
                    abs[:,t,:] = abs[:,t-self.ls,:] + history[:, t, 1:]
                    # Update hidden states in the second pass
                    window_fake = self.window_TTA(history, abs, t)
                    self.forward_once_in_loop(window_fake, thread, update_h=True)
                else:
                    history[:, t, 1:] = self.forward_once_in_loop(window, thread, update_h=True)[:, -1]
                    abs[:,t,:] = abs[:,t-self.ls,:] + history[:, t, 1:]
            tick2 = time.time()
            print(f'Prediction time: {tick2-tick1:.2f} seconds')

            # 3. Post-process - extract predictions
            for t in range(self.hs, self.hs + ground_truth.shape[1]):
                history[:, t, 1:] += history[:, t-self.ls, 1:]
            
            predictions = history[:, self.hs:]
            # Copy first ground truth value (initial condition)
            predictions[:, 0] = ground_truth[:, 0]
            
            # Convert to numpy for return
            predictions = self.filt(predictions.detach().cpu().numpy())
            ground_truth = ground_truth.detach().cpu().numpy()
            
        return predictions, ground_truth, sequence_lengths
    
    
    def forward_once_in_loop(self, window, thread=0, update_h=True):
        bs = window.shape[0]
        x = torch.cat([
            window[:, :, :1].expand(-1, -1, self.pred_dim),
            window[:, :, 1:1+self.pred_dim] * self.r_max,
        ], -1)
        
        # symmetrized model
        if self.sym:
            for i, layer in enumerate(self.rnns):
                # Properly handle hidden states for symmetry
                if i == 0:
                    h_pos = self.h[thread][i]
                    # h_neg = self.h_neg[thread][i]
                    h_neg = -self.h[thread][i]
                    h_in = torch.cat([h_pos, h_neg], dim=1)
                    x, state = layer(x, h_in)
                
                if update_h:
                    self.h[thread][i] = state['h_pos']
                    # self.h_neg[thread][i] = state['h_neg']
            
            out = self.fc(x)
            
            return out
        
        # base model
        x_neg = torch.cat([
            -window[:, :, :1].expand(-1, -1, self.pred_dim),
            torch.flip(window[:, :, 1:1+self.pred_dim], dims=[-1]) * -self.r_max,
        ], -1)
        x = torch.cat([x, x_neg], dim=0)
        
        for i, layer in enumerate(self.rnns):
            # For base model, ensure proper hidden state handling for symmetry
            h_pos = self.h[thread][i]
            h_neg = self.h_neg[thread][i]
            h_in = torch.cat([h_pos, h_neg], dim=1)
            x, state = layer(x, h_in)
                
            if update_h:
                self.h[thread][i] = state[:, :bs]
                self.h_neg[thread][i] = state[:, bs:]
        
        out = self.fc(x)
        out = 0.5 * (out[:bs] - out[bs:])
        return out

    def window_TTA(self, history, abs, t):
        t0 = t - self.ls - 1
        t1 = t - self.ls
        t2 = t - self.ls + 2
        # acc_fake = history[:, t0:t2, :1].mean(1, keepdim=True)
        acc_fake = history[:, t0:t0+1, :1]/4 + history[:, t1+1:t2, :1]/4 + history[:, t1:t1+1, :1]/2
        dis_real = abs[:, t1:t1+1, :]
        return torch.cat((acc_fake, dis_real), -1)
        
    def filt(self, x, fp=12.5):
        if self.ls > 1:
            for e in range(x.shape[0]):
                for floor in range(x.shape[-1]-1):
                    x[e, :, floor+1] = lowpass(x[e, :, floor+1], fp, 50)
        return x
    
    def reset_hidden_states(self, h=None, c=None, h_neg=None, c_neg=None):
        """
        Reset LSTM hidden and cell states to zeros or provided values.
        
        For symmetry constraint f(x) = -f(-x') to hold with non-zero hidden states,
        the hidden states must satisfy: h_neg = -h_pos
        
        Creates tensors with dimensions:
        - ls: number of threads
        - layers: number of LSTM layers
        - 1: number of directions (unidirectional)
        - bs: batch size
        - width: hidden dimension size
        """
        shape = (self.ls, self.layers, 1, self.bs, self.width)
        
        if h is None:
            self.h = torch.zeros(*shape).cuda()
            self.h_neg = torch.zeros(*shape).cuda()
        else:
            self.h = h
            # For symmetry constraint, h_neg should be -h when h is provided
            self.h_neg = -h if h_neg is None else h_neg
            
        if c is None:
            self.c = torch.zeros(*shape).cuda()
            self.c_neg = torch.zeros(*shape).cuda()
        else:
            self.c = c
            # For symmetry constraint, c_neg should be -c when c is provided
            self.c_neg = -c if c_neg is None else c_neg

        return
    
    
    def pre_process(self, data_path):
        """
        Prepare data for model prediction.
        
        This function loads test data, applies necessary preprocessing steps, and
        formats the data for autoregressive prediction with appropriate padding.
        
        Parameters:
        -----------
        data_path : str
            Path to test data directory
            
        Returns:
        --------
        tuple
            (processed_input, ground_truth, sequence_lengths)
        """
        with torch.no_grad():
            # Load and scale test data (ground_truth)
            gt = load_test_data(data_path, self.scaler_path, self.args)
        
            # Set sequence lengths and extract dimensions
            sequence_lengths = [gt.shape[1]] * gt.shape[0]
            self.bs, n_timesteps, n_floors = gt[..., 1:].shape
            
            # Create history padding (head) and append to real data (body)
            # Format: [acceleration, displacement1, displacement2, ...]
            head = np.concatenate([
                np.zeros((self.bs, self.hs, 1)),            # Zero acceleration
                np.concatenate([gt[:, :1, 1:]]*self.hs, 1)  # Init displacements
            ], axis=2)
            
            body = np.concatenate([
                gt[..., :1],                                # Acceleration
                np.ones((self.bs, n_timesteps, n_floors))   # Unit displacements
            ], axis=2)
            
            history = np.concatenate((head, body), axis=1)
            
            # Convert to PyTorch tensors
            history = torch.Tensor(history).cuda()
            gt = torch.Tensor(gt).cuda()
            
        return history, gt, sequence_lengths


# Part 3: Helper Functions
# =======================

def update_state_dict(mdict, sym=True):
    """
    Update state dictionary keys to match current model structure.
    
    This function is used to load weights from models with different
    layer naming conventions, particularly when loading TrainingLSTM weights
    for use in a InferenceLSTM model.
    
    Parameters:
    -----------
    mdict : dict
        Model state dictionary
    layers : int
        Number of hidden layers
        
    Returns:
    --------
    dict
        Updated state dictionary with standardized keys
    """
    
    mdict = {k.replace('_orig_mod.', ''): v for k, v in mdict.items()}
    max_layer = max([int(k.replace('hidden.', '')[0]) if 'hidden' in k else -1 for k in mdict.keys()]) + 2
    # print(mdict.keys())
    # Rename input layer keys
    if not sym:
        mdict['rnns.0.weight_hh_l0'] = mdict.pop('head.weight_hh_l0')
        mdict['rnns.0.weight_ih_l0'] = mdict.pop('head.weight_ih_l0')
        mdict['rnns.0.bias_hh_l0']   = mdict.pop('head.bias_hh_l0')
        mdict['rnns.0.bias_ih_l0']   = mdict.pop('head.bias_ih_l0')
        
        # Rename hidden layer keys
        for i in range(max_layer - 1):
            mdict[f'rnns.{i+1}.weight_hh_l0'] = mdict.pop(f'hidden.{i}.weight_hh_l0')
            mdict[f'rnns.{i+1}.weight_ih_l0'] = mdict.pop(f'hidden.{i}.weight_ih_l0')
            mdict[f'rnns.{i+1}.bias_hh_l0']   = mdict.pop(f'hidden.{i}.bias_hh_l0')
            mdict[f'rnns.{i+1}.bias_ih_l0']   = mdict.pop(f'hidden.{i}.bias_ih_l0')
    else:
        mdict['rnns.0.rnn.weight_hh_l0'] = mdict.pop('head.rnn.weight_hh_l0')
        mdict['rnns.0.rnn.weight_ih_l0'] = mdict.pop('head.rnn.weight_ih_l0')
        mdict['rnns.0.rnn.bias_hh_l0']   = mdict.pop('head.rnn.bias_hh_l0')
        mdict['rnns.0.rnn.bias_ih_l0']   = mdict.pop('head.rnn.bias_ih_l0')
        
        # Rename hidden layer keys
        for i in range(max_layer - 1):
            mdict[f'rnns.{i+1}.rnn.weight_hh_l0'] = mdict.pop(f'hidden.{i}.rnn.weight_hh_l0')
            mdict[f'rnns.{i+1}.rnn.weight_ih_l0'] = mdict.pop(f'hidden.{i}.rnn.weight_ih_l0')
            mdict[f'rnns.{i+1}.rnn.bias_hh_l0']   = mdict.pop(f'hidden.{i}.rnn.bias_hh_l0')
            mdict[f'rnns.{i+1}.rnn.bias_ih_l0']   = mdict.pop(f'hidden.{i}.rnn.bias_ih_l0')
    return mdict


def load_test_data(path='data/mdof-3/', scaler_path=None, args=None):
    """
    Load and preprocess test data from MAT files.
    """
    ls, pdim = args.local_sample_step, args.pred_dim
    idx = args.bridge_idx
    res = args.response
    # Load acceleration and displacement data
    a = np.load(path + 'acc_tes_50Hz_0.15g.npy')[..., None]
    d = np.load(path + f'dis_{res}/dis_{res}_bridge_{idx}_tes_50Hz_0.15g.npy')
    
    # a = np.load(path + 'acc_tra_50Hz_0.3g.npy')[..., None]
    # d = np.load(path + f'dis_{res}/dis_{res}_bridge_{idx}_tra_50Hz_0.3g.npy')
    
    if res == 'bear' and d.shape[-1] == 6:
        d = d[..., [1, 4]]  # (n_eq, n_step, 2)
    
    # Combine acceleration and displacement
    h = np.concatenate((a, d), axis=-1)
    h = np.swapaxes(h, 1, 2)  # N, C, T
    
    # Scale data
    scaler = Scaler(ls, scaler_path + '/scaler.npy')
    h = scaler.scale(h)[:, :1+pdim]
    h = np.swapaxes(h, 1, 2)
    
    return h


def load_args(ph=None, ls=None, path=None):
    """
    Load or create model configuration.
    """
    if path is not None:
        # Load saved configuration
        args = joblib.load(path + '/configs.save')
    else:
        # Create new configuration
        from parse import parse_args
        args = parse_args()
    
    # import os, json
    # idx = args.bridge_idx
    # config_dir = 'data/configs'
    # target_prefix = f'bridge-{idx}-'
    # config_path = next((fn for fn in os.listdir(config_dir) if fn.startswith(target_prefix)), None)
    
    # if not os.path.exists(os.path.join(config_dir, config_path)):
    #     raise FileNotFoundError(f"配置文件 {config_path} 不存在")

    # with open(os.path.join(config_dir, config_path), "r", encoding="utf-8") as f:
    #     config = json.load(f)
    
    args.flip = True
    args.fliplr = True
    print('Using symmetric recursive prediction...')
        
    if ls is not None:
        args.local_sample_step = ls
    if ph is not None:
        args.past_history = ph
    
    return args



