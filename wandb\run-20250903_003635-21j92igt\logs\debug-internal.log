{"time":"2025-09-03T00:36:35.4171996+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-09-03T00:36:36.4913002+08:00","level":"INFO","msg":"stream: created new stream","id":"21j92igt"}
{"time":"2025-09-03T00:36:36.4913002+08:00","level":"INFO","msg":"stream: started","id":"21j92igt"}
{"time":"2025-09-03T00:36:36.4913002+08:00","level":"INFO","msg":"handler: started","stream_id":"21j92igt"}
{"time":"2025-09-03T00:36:36.4913002+08:00","level":"INFO","msg":"sender: started","stream_id":"21j92igt"}
{"time":"2025-09-03T00:36:36.4913002+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"21j92igt"}
{"time":"2025-09-03T00:36:36.940032+08:00","level":"ERROR","msg":"git repo not found","error":"repository does not exist"}
{"time":"2025-09-03T01:16:08.1831456+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T01:18:41.031938+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T01:19:04.8295469+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": unexpected EOF"}
{"time":"2025-09-03T01:20:41.075053+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T01:25:02.6667705+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": unexpected EOF"}
{"time":"2025-09-03T05:42:37.4520252+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": unexpected EOF"}
{"time":"2025-09-03T09:05:41.3074045+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T09:06:41.800753+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T09:07:03.4210249+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T09:08:26.6060114+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T09:24:57.4121463+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T09:26:14.4901511+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T09:35:41.2934004+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T09:51:41.4988886+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T09:55:26.7795925+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T10:16:21.833733+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": unexpected EOF"}
{"time":"2025-09-03T10:40:11.3020291+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T10:44:57.5429269+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T11:26:56.3039718+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T11:35:41.6983688+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T11:49:44.144605+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T12:00:27.4116722+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T12:08:13.4415129+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T12:23:45.4269536+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T12:24:12.4133378+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T12:25:11.9160852+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": unexpected EOF"}
{"time":"2025-09-03T12:26:42.4173112+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T12:42:43.9089931+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T12:44:43.5447667+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": unexpected EOF"}
{"time":"2025-09-03T12:49:37.7513713+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T12:52:13.6815757+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": unexpected EOF"}
{"time":"2025-09-03T12:55:27.883198+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T12:56:12.8766196+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T13:21:28.6635174+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T13:24:42.4164205+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream\": EOF"}
{"time":"2025-09-03T13:51:38.9354949+08:00","level":"ERROR","msg":"HTTP error","status":404,"method":"POST","url":"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/21j92igt/file_stream"}
{"time":"2025-09-03T13:51:38.9361192+08:00","level":"ERROR+4","msg":"filestream: fatal error: filestream: failed to upload: 404 Not Found path=files/xzk8559/recLSTM-bridge/21j92igt/file_stream: {\"error\":\"run recLSTM-bridge/21j92igt not found while streaming file\"}"}
