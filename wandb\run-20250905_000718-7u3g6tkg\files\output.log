Shape of input  per step: torch.Size([120, 5])
Shape of output per step: torch.Size([120, 2])
Dataset length: 417330
Shape of input  per step: torch.Size([120, 5])
Shape of output per step: torch.Size([120, 2])
Dataset length: 395199
TrainingRNN(
  (head): SymmetrizedBlock(
    (rnn): GRU(4, 256, batch_first=True)
  )
  (hidden): ModuleList(
    (0-2): 3 x SymmetrizedBlock(
      (rnn): GRU(256, 256, batch_first=True)
    )
  )
  (fc): Sequential(
    (0): Linear(in_features=256, out_features=256, bias=False)
    (1): Tanh()
    (2): Linear(in_features=256, out_features=2, bias=False)
  )
)
1451520 paramerters in total
0	0.00642089	0.00581116	0.00100	7.65
Using symmetric recursive prediction...
Loading single model from epoch best...
dict_keys(['head.rnn.weight_ih_l0', 'head.rnn.weight_hh_l0', 'head.rnn.bias_ih_l0', 'head.rnn.bias_hh_l0', 'hidden.0.rnn.weight_ih_l0', 'hidden.0.rnn.weight_hh_l0', 'hidden.0.rnn.bias_ih_l0', 'hidden.0.rnn.bias_hh_l0', 'hidden.1.rnn.weight_ih_l0', 'hidden.1.rnn.weight_hh_l0', 'hidden.1.rnn.bias_ih_l0', 'hidden.1.rnn.bias_hh_l0', 'hidden.2.rnn.weight_ih_l0', 'hidden.2.rnn.weight_hh_l0', 'hidden.2.rnn.bias_ih_l0', 'hidden.2.rnn.bias_hh_l0', 'fc.0.weight', 'fc.2.weight'])
Traceback (most recent call last):
  File "E:\Codes\recLSTM-bridge\train.py", line 161, in <module>
    run(args)
  File "E:\Codes\recLSTM-bridge\train.py", line 98, in run
    rec_results = run_prediction(
  File "E:\Codes\recLSTM-bridge\test.py", line 173, in run_prediction
    model.load_state_dict(update_state_dict(state_dict))
  File "E:\Codes\recLSTM-bridge\utils_model_fast.py", line 296, in update_state_dict
    mdict[f'rnns.{i+1}.weight_hh_l0'] = mdict.pop(f'hidden.rnn.{i}.weight_hh_l0')
KeyError: 'hidden.rnn.0.weight_hh_l0'
