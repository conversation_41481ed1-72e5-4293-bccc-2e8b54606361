{"time":"2025-09-08T01:13:04.6211929+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-09-08T01:13:05.7707405+08:00","level":"INFO","msg":"stream: created new stream","id":"3hx35wd9"}
{"time":"2025-09-08T01:13:05.7707405+08:00","level":"INFO","msg":"stream: started","id":"3hx35wd9"}
{"time":"2025-09-08T01:13:05.7707405+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"3hx35wd9"}
{"time":"2025-09-08T01:13:05.7712676+08:00","level":"INFO","msg":"handler: started","stream_id":"3hx35wd9"}
{"time":"2025-09-08T01:13:05.7712676+08:00","level":"INFO","msg":"sender: started","stream_id":"3hx35wd9"}
{"time":"2025-09-08T01:13:06.2973262+08:00","level":"ERROR","msg":"git repo not found","error":"repository does not exist"}
{"time":"2025-09-08T03:46:22.2893469+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/3hx35wd9/file_stream\": unexpected EOF"}
{"time":"2025-09-08T09:05:03.3517155+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/3hx35wd9/file_stream\": unexpected EOF"}
