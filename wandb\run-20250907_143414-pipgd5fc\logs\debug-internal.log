{"time":"2025-09-07T14:34:15.3007795+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-09-07T14:34:17.353365+08:00","level":"INFO","msg":"stream: created new stream","id":"pipgd5fc"}
{"time":"2025-09-07T14:34:17.353365+08:00","level":"INFO","msg":"stream: started","id":"pipgd5fc"}
{"time":"2025-09-07T14:34:17.353365+08:00","level":"INFO","msg":"handler: started","stream_id":"pipgd5fc"}
{"time":"2025-09-07T14:34:17.353365+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"pipgd5fc"}
{"time":"2025-09-07T14:34:17.353365+08:00","level":"INFO","msg":"sender: started","stream_id":"pipgd5fc"}
{"time":"2025-09-07T14:34:20.3543317+08:00","level":"ERROR","msg":"git repo not found","error":"repository does not exist"}
{"time":"2025-09-07T14:45:55.0140349+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/pipgd5fc/file_stream\": unexpected EOF"}
{"time":"2025-09-07T14:51:40.0054855+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/pipgd5fc/file_stream\": EOF"}
{"time":"2025-09-07T14:56:36.3077701+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/pipgd5fc/file_stream\": unexpected EOF"}
{"time":"2025-09-07T15:00:35.2615931+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/pipgd5fc/file_stream\": unexpected EOF"}
{"time":"2025-09-07T15:08:33.6551782+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/pipgd5fc/file_stream\": EOF"}
{"time":"2025-09-07T15:09:27.2349842+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/pipgd5fc/file_stream\": EOF"}
{"time":"2025-09-07T15:15:12.2285451+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/pipgd5fc/file_stream\": EOF"}
{"time":"2025-09-07T15:18:43.9483941+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/pipgd5fc/file_stream\": unexpected EOF"}
{"time":"2025-09-07T15:24:42.8864781+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/pipgd5fc/file_stream\": unexpected EOF"}
{"time":"2025-09-07T15:34:50.4586806+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/pipgd5fc/file_stream\": unexpected EOF"}
{"time":"2025-09-07T15:39:34.7569032+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/pipgd5fc/file_stream\": EOF"}
{"time":"2025-09-07T16:01:53.4195477+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/pipgd5fc/file_stream\": EOF"}
{"time":"2025-09-07T16:11:46.3203803+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": unexpected EOF"}
{"time":"2025-09-07T16:23:19.6123126+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/pipgd5fc/file_stream\": EOF"}
{"time":"2025-09-07T16:24:59.133818+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/pipgd5fc/file_stream\": unexpected EOF"}
{"time":"2025-09-07T16:28:13.2139769+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/pipgd5fc/file_stream\": EOF"}
{"time":"2025-09-07T16:28:27.3994551+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/pipgd5fc/file_stream\": EOF"}
{"time":"2025-09-07T16:33:27.7460715+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/pipgd5fc/file_stream\": unexpected EOF"}
