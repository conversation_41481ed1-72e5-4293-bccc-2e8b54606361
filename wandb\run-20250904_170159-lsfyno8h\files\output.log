Shape of input  per step: torch.Size([120, 5])
Shape of output per step: torch.Size([120, 2])
Dataset length: 417330
Shape of input  per step: torch.Size([120, 5])
Shape of output per step: torch.Size([120, 2])
Dataset length: 395199
TrainingLSTM(
  (head): GRU(4, 256, batch_first=True)
  (hidden): ModuleList(
    (0-2): 3 x GRU(256, 256, batch_first=True)
  )
  (fc): Sequential(
    (0): Linear(in_features=256, out_features=256, bias=True)
    (1): Tanh()
    (2): Linear(in_features=256, out_features=2, bias=True)
  )
)
1451778 paramerters in total
0	0.73523343	0.73345256	0.00100	4.67
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.50 seconds
Model Performance:
- Correlation: 0.06285
- Root Mean Squared Error: 1.65 mm
- Peak Error: 96.065 %
D:\anaconda3\envs\torch22\lib\site-packages\torch\optim\lr_scheduler.py:143: UserWarning: Detected call of `lr_scheduler.step()` before `optimizer.step()`. In PyTorch 1.1.0 and later, you should call them in the opposite order: `optimizer.step()` before `lr_scheduler.step()`.  Failure to do this will result in PyTorch skipping the first value of the learning rate schedule. See more details at https://pytorch.org/docs/stable/optim.html#how-to-adjust-learning-rate
  warnings.warn("Detected call of `lr_scheduler.step()` before `optimizer.step()`. "
1	0.00576877	0.00569231	0.00100	37.36
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 20.00 seconds
Model Performance:
- Correlation: 0.36663
- Root Mean Squared Error: 1.61 mm
- Peak Error: 86.230 %
2	0.00252395	0.00268047	0.00099	71.06
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.39 seconds
Model Performance:
- Correlation: 0.42820
- Root Mean Squared Error: 4.15 mm
- Peak Error: 120.235 %
3	0.00141091	0.00143652	0.00099	102.99
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.53 seconds
Model Performance:
- Correlation: 0.68747
- Root Mean Squared Error: 1.90 mm
- Peak Error: 51.056 %
4	0.00108245	0.00123920	0.00098	136.17
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.67 seconds
Model Performance:
- Correlation: 0.73807
- Root Mean Squared Error: 1.44 mm
- Peak Error: 20.628 %
5	0.00081384	0.00111542	0.00098	165.89
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.14 seconds
Model Performance:
- Correlation: 0.65728
- Root Mean Squared Error: 1.68 mm
- Peak Error: 23.472 %
6	0.00188402	0.00156833	0.00097	198.47
7	0.00120027	0.00101570	0.00097	211.36
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 21.38 seconds
Model Performance:
- Correlation: 0.87129
- Root Mean Squared Error: 1.09 mm
- Peak Error: 15.547 %
8	0.00107196	0.00094623	0.00096	246.39
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 20.02 seconds
Model Performance:
- Correlation: 0.86298
- Root Mean Squared Error: 1.12 mm
- Peak Error: 18.400 %
9	0.00070108	0.00099971	0.00096	279.52
10	0.00079828	0.00099399	0.00095	292.45
11	0.00078109	0.00099689	0.00095	305.38
12	0.00079155	0.00082735	0.00094	318.15
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.64 seconds
Model Performance:
- Correlation: 0.66410
- Root Mean Squared Error: 2.67 mm
- Peak Error: 54.778 %
13	0.00072659	0.00055711	0.00094	351.60
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.93 seconds
Model Performance:
- Correlation: 0.43317
- Root Mean Squared Error: 6.13 mm
- Peak Error: 206.420 %
14	0.00047640	0.00057471	0.00093	385.23
15	0.00134498	0.00054519	0.00093	398.68
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 21.50 seconds
Model Performance:
- Correlation: 0.78993
- Root Mean Squared Error: 1.48 mm
- Peak Error: 17.155 %
16	0.00039960	0.00055277	0.00093	433.89
17	0.00045580	0.00063579	0.00092	446.96
18	0.00043750	0.00061808	0.00092	459.53
19	0.00076412	0.00078031	0.00091	472.41
20	0.00063302	0.00040115	0.00091	485.92
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.32 seconds
Model Performance:
- Correlation: 0.92376
- Root Mean Squared Error: 1.02 mm
- Peak Error: 13.865 %
21	0.00048536	0.00051303	0.00090	516.34
22	0.00053166	0.00059534	0.00090	527.66
23	0.00041456	0.00043265	0.00090	539.07
24	0.00066472	0.00050197	0.00089	551.73
25	0.00032859	0.00046389	0.00089	564.31
26	0.00046646	0.00049503	0.00088	576.90
27	0.00051284	0.00045572	0.00088	589.65
28	0.00067195	0.00068369	0.00088	602.71
29	0.00045746	0.00037105	0.00087	616.02
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 20.30 seconds
Model Performance:
- Correlation: 0.89546
- Root Mean Squared Error: 1.11 mm
- Peak Error: 15.347 %
30	0.00031340	0.00041782	0.00087	650.19
31	0.00071181	0.00036826	0.00087	663.12
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 20.79 seconds
Model Performance:
- Correlation: 0.89910
- Root Mean Squared Error: 1.11 mm
- Peak Error: 15.198 %
32	0.00036172	0.00037597	0.00086	697.60
33	0.00011754	0.00020621	0.00086	710.14
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.82 seconds
Model Performance:
- Correlation: 0.93297
- Root Mean Squared Error: 0.96 mm
- Peak Error: 13.434 %
34	0.00015797	0.00024213	0.00085	741.80
35	0.00012000	0.00017773	0.00085	754.36
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 20.91 seconds
Model Performance:
- Correlation: 0.94022
- Root Mean Squared Error: 0.93 mm
- Peak Error: 12.608 %
36	0.00082104	0.00023811	0.00085	788.98
37	0.00036335	0.00037523	0.00084	801.41
38	0.00013608	0.00019861	0.00084	814.05
39	0.00020202	0.00019517	0.00084	826.63
40	0.00008574	0.00016494	0.00083	839.43
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.60 seconds
Model Performance:
- Correlation: 0.90966
- Root Mean Squared Error: 1.06 mm
- Peak Error: 14.674 %
41	0.00047307	0.00029754	0.00083	873.03
42	0.00015367	0.00018394	0.00083	886.05
43	0.00018995	0.00016697	0.00082	899.10
44	0.00021200	0.00019284	0.00082	912.97
45	0.00013586	0.00019962	0.00082	926.64
46	0.00015799	0.00019993	0.00081	939.25
47	0.00011821	0.00015318	0.00081	952.07
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 21.19 seconds
Model Performance:
- Correlation: 0.92557
- Root Mean Squared Error: 0.97 mm
- Peak Error: 14.143 %
48	0.00022157	0.00030806	0.00081	986.93
49	0.00028972	0.00030962	0.00080	999.53
50	0.00038418	0.00020024	0.00080	1012.33
51	0.00034674	0.00022261	0.00080	1025.27
52	0.00012299	0.00016793	0.00079	1038.31
53	0.00022134	0.00014503	0.00079	1051.06
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.79 seconds
Model Performance:
- Correlation: 0.91541
- Root Mean Squared Error: 0.98 mm
- Peak Error: 15.638 %
54	0.00012086	0.00015373	0.00079	1084.10
55	0.00040515	0.00044663	0.00078	1096.57
56	0.00012596	0.00018775	0.00078	1108.86
57	0.00014327	0.00016264	0.00078	1121.13
58	0.00011838	0.00014955	0.00078	1133.66
59	0.00045383	0.00031751	0.00077	1146.01
60	0.00011802	0.00015760	0.00077	1158.27
61	0.00013268	0.00016381	0.00077	1170.56
62	0.00018632	0.00018603	0.00076	1183.15
63	0.00013635	0.00018161	0.00076	1195.71
64	0.00011208	0.00017525	0.00076	1208.30
65	0.00021354	0.00019717	0.00075	1221.10
66	0.00052956	0.00038160	0.00075	1233.89
67	0.00018966	0.00025068	0.00075	1246.76
68	0.00009817	0.00016178	0.00075	1259.42
69	0.00010282	0.00013364	0.00074	1272.37
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 20.52 seconds
Model Performance:
- Correlation: 0.80339
- Root Mean Squared Error: 59.58 mm
- Peak Error: 526.750 %
70	0.00011991	0.00014837	0.00074	1306.30
71	0.00012571	0.00014119	0.00074	1319.26
72	0.00022470	0.00017175	0.00074	1332.73
73	0.00010100	0.00011718	0.00073	1345.85
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 20.33 seconds
Model Performance:
- Correlation: 0.84954
- Root Mean Squared Error: 1.28 mm
- Peak Error: 17.201 %
74	0.00011946	0.00012067	0.00073	1380.47
75	0.00018961	0.00013618	0.00073	1393.59
76	0.00012224	0.00011070	0.00072	1406.41
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.99 seconds
Model Performance:
- Correlation: 0.84935
- Root Mean Squared Error: 214.90 mm
- Peak Error: 3057.917 %
77	0.00021581	0.00024238	0.00072	1439.74
78	0.00011070	0.00012504	0.00072	1452.29
79	0.00009020	0.00011908	0.00072	1464.88
80	0.00010382	0.00011436	0.00071	1477.23
81	0.00008988	0.00011967	0.00071	1489.93
82	0.00026420	0.00022274	0.00071	1502.72
83	0.00012007	0.00011439	0.00071	1515.56
84	0.00008417	0.00016428	0.00070	1528.30
85	0.00009065	0.00012023	0.00070	1541.24
86	0.00010677	0.00012818	0.00070	1553.86
87	0.00014892	0.00013263	0.00070	1566.46
88	0.00013861	0.00014762	0.00069	1579.01
89	0.00014705	0.00014424	0.00069	1592.08
90	0.00048407	0.00012571	0.00069	1604.90
91	0.00016961	0.00012530	0.00069	1617.95
92	0.00011212	0.00011830	0.00068	1630.76
93	0.00010772	0.00010862	0.00068	1643.90
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.67 seconds
Model Performance:
- Correlation: 0.79919
- Root Mean Squared Error: 316.09 mm
- Peak Error: 4054.788 %
94	0.00010434	0.00011055	0.00068	1676.15
95	0.00008758	0.00012198	0.00068	1689.16
96	0.00006490	0.00010570	0.00068	1702.18
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.54 seconds
Model Performance:
- Correlation: 0.86050
- Root Mean Squared Error: 110.96 mm
- Peak Error: 829.490 %
97	0.00013858	0.00014512	0.00067	1735.33
98	0.00058714	0.00010023	0.00067	1748.50
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 20.46 seconds
Model Performance:
- Correlation: 0.89054
- Root Mean Squared Error: 1.18 mm
- Peak Error: 13.070 %
99	0.00011694	0.00013689	0.00067	1782.76
100	0.00012404	0.00010716	0.00067	1795.23
101	0.00016063	0.00017399	0.00066	1807.67
102	0.00011351	0.00011634	0.00066	1820.19
103	0.00009611	0.00008468	0.00066	1833.09
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 20.62 seconds
Model Performance:
- Correlation: 0.87956
- Root Mean Squared Error: 313.38 mm
- Peak Error: 3970.259 %
104	0.00006164	0.00008699	0.00066	1867.41
105	0.00008794	0.00012718	0.00066	1880.36
106	0.00005325	0.00007396	0.00065	1892.99
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 19.05 seconds
Model Performance:
- Correlation: 0.86281
- Root Mean Squared Error: 406.80 mm
- Peak Error: 5344.367 %
107	0.00005881	0.00010017	0.00065	1925.31
108	0.00006993	0.00008942	0.00065	1937.58
109	0.00010698	0.00010644	0.00065	1950.46
110	0.00006126	0.00011938	0.00065	1963.28
111	0.00012132	0.00009174	0.00064	1976.30
112	0.00006271	0.00005972	0.00064	1988.98
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.30 seconds
Model Performance:
- Correlation: 0.55274
- Root Mean Squared Error: 848.72 mm
- Peak Error: 29066.206 %
113	0.00005187	0.00005843	0.00064	2019.75
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.76 seconds
Model Performance:
- Correlation: 0.91469
- Root Mean Squared Error: 180.61 mm
- Peak Error: 1145.032 %
114	0.00005073	0.00008864	0.00064	2050.63
115	0.00004289	0.00006308	0.00063	2061.99
116	0.00006149	0.00007033	0.00063	2073.31
117	0.00012427	0.00011362	0.00063	2084.74
118	0.00004815	0.00005519	0.00063	2096.18
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 16.67 seconds
Model Performance:
- Correlation: 0.77258
- Root Mean Squared Error: 505.33 mm
- Peak Error: 11940.410 %
119	0.00004412	0.00005710	0.00063	2124.60
120	0.00004097	0.00005754	0.00063	2135.94
121	0.00010791	0.00008196	0.00062	2147.35
122	0.00009679	0.00007963	0.00062	2158.80
123	0.00004649	0.00005083	0.00062	2170.15
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.49 seconds
Model Performance:
- Correlation: 0.88585
- Root Mean Squared Error: 195.17 mm
- Peak Error: 1206.427 %
124	0.00014760	0.00017038	0.00062	2200.67
125	0.00007738	0.00006799	0.00062	2212.14
126	0.00005087	0.00005500	0.00061	2223.54
127	0.00003763	0.00004040	0.00061	2234.97
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 17.20 seconds
Model Performance:
- Correlation: 0.77087
- Root Mean Squared Error: 301.12 mm
- Peak Error: 7079.246 %
128	0.00004181	0.00004358	0.00061	2264.17
129	0.00008198	0.00007747	0.00061	2275.91
130	0.00004578	0.00006158	0.00061	2287.37
131	0.00006966	0.00006793	0.00060	2298.85
132	0.00005172	0.00003535	0.00060	2310.29
Using symmetric recursive prediction...
Loading single model from epoch best...
Prediction time: 18.21 seconds
Model Performance:
- Correlation: 0.88549
- Root Mean Squared Error: 163.51 mm
- Peak Error: 1075.602 %
133	0.00003505	0.00004948	0.00060	2340.69
134	0.00008849	0.00006579	0.00060	2352.20
135	0.00002909	0.00003741	0.00060	2363.77
136	0.00002434	0.00004424	0.00060	2375.18
137	0.00002679	0.00003799	0.00059	2386.50
