{"time":"2025-09-07T19:12:38.9612607+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-09-07T19:12:41.5917175+08:00","level":"INFO","msg":"stream: created new stream","id":"wkyneacn"}
{"time":"2025-09-07T19:12:41.5917175+08:00","level":"INFO","msg":"stream: started","id":"wkyneacn"}
{"time":"2025-09-07T19:12:41.5917175+08:00","level":"INFO","msg":"handler: started","stream_id":"wkyneacn"}
{"time":"2025-09-07T19:12:41.5917175+08:00","level":"INFO","msg":"sender: started","stream_id":"wkyneacn"}
{"time":"2025-09-07T19:12:41.5917175+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"wkyneacn"}
{"time":"2025-09-07T19:12:54.792348+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/wkyneacn/file_stream\": EOF"}
{"time":"2025-09-07T19:29:21.4021351+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/wkyneacn/file_stream\": EOF"}
{"time":"2025-09-07T19:34:58.0657994+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:35:13.0657538+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:35:18.579393+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/wkyneacn/file_stream\": EOF"}
{"time":"2025-09-07T19:35:28.0660343+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:35:43.0657738+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:35:58.0660077+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:36:13.0658006+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:36:28.0656922+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:36:43.0663098+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:36:58.0657259+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:37:13.0657109+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:37:28.0656757+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:37:43.06592+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:37:58.0658703+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:38:13.0658291+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:38:28.0655607+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:38:43.0655222+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:38:58.0660686+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:39:13.0656115+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:39:28.0660019+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:39:43.0659889+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:39:58.0654338+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:40:13.0658645+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:40:28.0657073+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:40:43.0655081+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:40:58.0656734+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:41:13.0659076+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:41:28.0656449+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:41:43.0662673+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:41:58.0656591+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:42:13.0657686+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:42:28.0657221+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:42:43.0658855+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:42:58.0655996+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:43:13.0654503+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:43:28.0654988+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:43:43.0657455+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:43:58.065596+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:44:13.065985+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:44:20.8131482+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/wkyneacn/file_stream\": EOF"}
{"time":"2025-09-07T19:44:28.0688924+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:44:43.0654916+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:44:58.0655992+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:45:13.0656805+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:45:28.0656682+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:45:43.0657541+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:45:58.0655159+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:46:13.0656092+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:46:28.0654705+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:46:43.0658638+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:46:58.0655825+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:47:13.0656612+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:47:28.066052+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:47:43.0659664+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:47:58.0659105+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:48:13.0658433+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:48:28.0658736+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:48:43.0655979+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:48:58.0658909+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:49:13.0656082+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:49:28.0656105+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:49:43.0657199+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:49:58.065518+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:50:13.0661049+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:50:28.0658964+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:50:43.0657399+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:50:58.0658334+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:51:13.0654522+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:51:28.0658807+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:51:43.0657401+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:51:58.0656628+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:52:13.0654734+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:52:28.0656283+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:52:43.0657056+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:52:58.0660801+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:53:13.0658886+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:53:28.0654134+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:53:43.0654566+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:53:58.0656676+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:54:13.0658269+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:54:28.0658429+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:54:43.0657855+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:54:58.0657113+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:55:13.0658282+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:55:28.0655036+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:55:43.0656967+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:55:58.0660927+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:56:13.0657582+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:56:28.0658973+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:56:43.0657443+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:56:58.0658661+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:57:13.0659841+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:57:28.065781+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:57:43.0657627+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:57:58.0659229+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:58:13.0655109+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:58:28.065672+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:58:43.0654873+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:58:58.0657974+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:59:13.065659+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:59:43.0656244+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T19:59:58.0656332+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:00:13.0657224+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:00:28.0657436+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:00:43.065732+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:00:58.0655066+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:01:13.0654901+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:01:28.0654194+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:01:43.0659822+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:01:58.0654932+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:02:13.0658486+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:02:28.0655467+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:02:43.0655879+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:02:58.0658456+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:03:13.0657599+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:03:28.0655679+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:03:43.0658503+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:03:58.0657195+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:04:13.0654629+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:04:28.0658445+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:04:43.0658848+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:04:58.0655922+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:05:13.0656872+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:05:28.0654744+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:05:43.0658161+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:05:58.0658187+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:06:13.0660834+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:06:28.0658426+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:06:43.0657517+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:06:58.0657279+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:07:13.0656694+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:07:28.0660601+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:07:43.0656348+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:07:58.0657591+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:08:13.0658332+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:08:28.0655726+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:08:43.0656069+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:08:58.0658558+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:09:13.0657034+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:09:28.0658681+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:09:43.0655926+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:09:58.0658816+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:10:13.065492+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:10:28.0656004+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:10:43.0655312+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:10:58.0654788+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:11:13.0656496+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:11:20.4231978+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/wkyneacn/file_stream\": EOF"}
{"time":"2025-09-07T20:11:28.0657536+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:11:43.0657234+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:11:58.0658099+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:12:13.0654874+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:12:28.0654036+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:12:43.0654543+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:12:58.0662061+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:13:13.0656978+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:13:28.0656474+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:13:43.0655+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:13:58.0656083+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:14:03.3922178+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/wkyneacn/file_stream\": EOF"}
{"time":"2025-09-07T20:14:13.0656502+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:14:28.0792779+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:14:43.0655642+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:14:58.065704+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:15:13.0656228+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:15:28.0656543+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:15:43.065741+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:15:58.0655554+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:16:13.065606+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:16:28.0657316+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:16:43.0655353+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:16:58.0658778+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:17:13.0658592+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:17:28.0656653+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:17:43.0655481+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:17:58.065668+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:18:13.06547+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:18:28.0658671+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:18:43.0657923+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:18:58.06567+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:19:13.0655484+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:19:28.0660919+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:19:43.0656318+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:19:58.0656424+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:20:13.0657199+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:20:28.0657642+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:20:43.06559+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:20:58.0658202+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:21:13.0657455+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:21:18.4360131+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/wkyneacn/file_stream\": EOF"}
{"time":"2025-09-07T20:21:28.065749+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:21:43.0654295+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:21:58.0658073+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:22:13.0657054+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:22:28.0655585+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:22:43.0656019+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:22:58.0659467+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:23:13.0658755+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:23:28.0656362+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:23:43.0657991+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:23:58.0658233+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:24:13.0658858+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:24:28.0657308+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:24:43.065986+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:24:58.0655671+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:25:13.0660211+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:25:28.0657441+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:25:43.0658301+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:25:58.065586+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:26:13.0656529+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:26:28.0657724+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:26:43.0657774+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:26:58.0658947+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:27:13.0656859+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:27:28.0655387+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:27:43.0658202+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:27:58.0655551+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:28:13.0656429+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:28:28.0654171+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:28:43.0655072+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:28:58.0658877+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:29:13.0657171+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:29:28.0695855+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:29:43.0657286+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:29:58.0654683+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:30:13.0657483+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:30:28.0656272+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:30:43.065793+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:30:51.0564805+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/wkyneacn/file_stream\": EOF"}
{"time":"2025-09-07T20:30:58.0657968+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:31:13.0655117+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:31:28.0654549+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:31:43.0656929+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:31:58.0655984+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:32:13.0654889+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:32:28.0654185+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:32:43.065621+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:32:58.0656956+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:33:13.0658595+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:33:28.0654742+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:33:43.0656302+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:33:58.0655255+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:34:13.0658847+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:34:28.0654347+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:34:43.0659306+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:34:58.0657893+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:35:13.0658083+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:35:28.0658491+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:35:43.0658636+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:35:58.065681+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:36:13.0654086+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:36:28.0657796+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:36:43.065832+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:36:58.0658191+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:37:13.0659043+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:37:28.0654446+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:37:43.0654207+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:37:58.0655386+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:38:13.0658131+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:38:28.0658564+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:38:43.0659332+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:38:58.0656325+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:39:13.0655532+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:39:28.065837+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:39:43.0656966+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:39:58.0655603+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:40:13.0654794+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:40:28.0655739+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:40:43.0657928+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:40:58.0654951+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:41:13.0654778+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:41:28.0657369+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:41:43.0655313+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:41:58.0655381+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:42:13.0653992+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:42:26.8192473+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/wkyneacn/file_stream\": unexpected EOF"}
{"time":"2025-09-07T20:42:28.0658661+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:42:43.0656133+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:42:58.06569+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:43:13.0656559+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:43:28.0655115+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:43:43.0657525+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:43:58.0655908+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:44:13.0658587+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:44:28.0697661+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:44:43.0655499+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:44:58.0658462+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:45:13.0657168+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:45:28.0658309+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:45:43.0658684+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:45:58.0655573+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:46:13.0657945+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:46:28.0656573+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:46:43.0657124+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:46:58.065625+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:47:13.0657159+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:47:28.065876+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:47:43.0654602+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:47:58.0658988+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:48:13.0658486+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:48:28.0656187+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:48:43.0655547+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:48:58.0664987+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:49:13.0658139+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:49:28.0656642+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:49:43.0657732+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:49:58.0655097+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:50:13.0656202+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:50:28.0655263+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:50:43.0654569+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:50:58.0654284+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:51:13.0655055+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:51:28.0658964+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:51:43.0654657+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:51:58.0654505+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:52:13.065947+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:52:28.0657232+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:52:43.0654898+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:52:58.065673+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:53:13.0657948+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:53:18.4110714+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/wkyneacn/file_stream\": EOF"}
{"time":"2025-09-07T20:53:28.0658221+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:53:43.0655469+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:53:58.0657025+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:54:13.0658541+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:54:28.0659629+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:54:43.0656357+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:54:58.0657488+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:55:13.0657332+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:55:28.0654329+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:55:43.065704+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:55:58.0658015+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:56:13.0658471+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:56:28.0655725+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:56:43.0656882+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:56:58.0659322+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:57:13.0657496+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:57:28.0656445+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:57:43.0657951+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:57:58.0658794+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:58:13.0656831+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:58:28.0661385+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:58:43.0658192+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:58:58.0655093+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:59:13.0660968+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:59:28.0660415+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:59:43.065908+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T20:59:58.0659156+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:00:13.065879+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:00:28.0654487+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:00:43.0658574+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:00:58.0657506+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:01:13.0656454+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:01:28.0664352+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:01:39.4856167+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/wkyneacn/file_stream\": EOF"}
{"time":"2025-09-07T21:01:43.0655527+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:01:58.0660198+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:02:13.0655316+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:02:28.0658718+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:02:43.0660786+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:02:58.0656468+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:03:13.0659226+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:03:28.0656222+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:03:43.0660616+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:03:58.065735+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:04:13.0658341+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:04:19.4156893+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/wkyneacn/file_stream\": EOF"}
{"time":"2025-09-07T21:04:28.0657969+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:04:43.0657447+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:04:58.0662601+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:05:13.0657277+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:05:28.0654141+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:05:43.0655883+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:05:58.0656677+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:06:13.0656234+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:06:28.0654353+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:06:43.0656425+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:06:58.0658718+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:07:13.0658793+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:07:28.0655022+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:07:43.0658844+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:07:58.0656298+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:08:13.0656845+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:08:28.0656217+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:08:43.0655218+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:08:58.0658489+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:09:13.0656796+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:09:28.0656712+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:09:43.0656047+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:09:58.06584+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:10:13.0659971+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:10:28.0654029+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:10:43.0655294+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:10:58.0656533+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:11:13.0655804+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:11:28.0655861+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:11:43.0658014+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:11:58.0658362+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:12:13.0654679+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:12:28.0655596+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:12:43.0656625+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:12:58.0655733+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:13:13.0658739+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:13:28.0654465+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:13:43.0657048+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:13:58.0657535+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:14:13.0656826+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:14:28.0778789+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:14:43.0659052+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:14:58.06569+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:15:13.0655803+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:15:28.0654885+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:15:43.0654937+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:15:58.0658261+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:16:13.0655637+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:16:28.065953+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:16:43.0654505+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:16:58.0658068+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:17:13.0656109+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:17:28.0656165+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:17:43.0660306+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:17:51.433349+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/wkyneacn/file_stream\": EOF"}
{"time":"2025-09-07T21:17:58.0654231+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:18:13.0656726+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:18:28.065654+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:18:43.0656384+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:18:58.0654775+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:19:13.0657854+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:19:28.0658614+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:19:43.0655474+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:19:58.0656681+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:20:13.0654801+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
{"time":"2025-09-07T21:20:28.0655698+08:00","level":"ERROR","msg":"monitor: error sampling metrics: process does not exist"}
