{"time":"2025-09-04T19:25:45.4753674+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-09-04T19:25:48.0604657+08:00","level":"INFO","msg":"stream: created new stream","id":"g69afuga"}
{"time":"2025-09-04T19:25:48.0604657+08:00","level":"INFO","msg":"stream: started","id":"g69afuga"}
{"time":"2025-09-04T19:25:48.0604657+08:00","level":"INFO","msg":"handler: started","stream_id":"g69afuga"}
{"time":"2025-09-04T19:25:48.0604657+08:00","level":"INFO","msg":"sender: started","stream_id":"g69afuga"}
{"time":"2025-09-04T19:25:48.0604657+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"g69afuga"}
{"time":"2025-09-04T19:25:48.7091388+08:00","level":"ERROR","msg":"git repo not found","error":"repository does not exist"}
{"time":"2025-09-04T19:39:08.6560223+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": EOF"}
{"time":"2025-09-04T19:58:24.696629+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": EOF"}
{"time":"2025-09-04T21:01:23.820491+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": unexpected EOF"}
{"time":"2025-09-04T21:22:44.6364634+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": unexpected EOF"}
{"time":"2025-09-04T21:27:27.3205056+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": unexpected EOF"}
{"time":"2025-09-04T21:29:17.6579743+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": unexpected EOF"}
{"time":"2025-09-04T21:30:47.5157457+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": unexpected EOF"}
{"time":"2025-09-04T21:37:50.4808933+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": EOF"}
{"time":"2025-09-04T21:40:35.5479294+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": EOF"}
{"time":"2025-09-04T21:51:05.4963188+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": unexpected EOF"}
{"time":"2025-09-04T22:11:53.0207091+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": unexpected EOF"}
{"time":"2025-09-04T22:29:22.2929731+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": EOF"}
{"time":"2025-09-04T22:29:29.0874958+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": EOF"}
{"time":"2025-09-04T22:37:07.3217952+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": EOF"}
{"time":"2025-09-04T22:44:31.2835612+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": unexpected EOF"}
{"time":"2025-09-04T22:44:39.0216962+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": EOF"}
{"time":"2025-09-04T23:05:30.8481476+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": EOF"}
{"time":"2025-09-04T23:06:31.8317268+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": EOF"}
{"time":"2025-09-04T23:09:23.2767243+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": EOF"}
{"time":"2025-09-04T23:13:56.9611711+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": unexpected EOF"}
{"time":"2025-09-04T23:15:58.157103+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": EOF"}
{"time":"2025-09-04T23:20:27.209717+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": unexpected EOF"}
{"time":"2025-09-04T23:27:40.7212843+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": EOF"}
{"time":"2025-09-04T23:31:58.8535769+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": net/http: request canceled (Client.Timeout exceeded while awaiting headers)"}
{"time":"2025-09-04T23:32:03.124266+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": unexpected EOF"}
{"time":"2025-09-04T23:34:50.383646+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": EOF"}
{"time":"2025-09-04T23:37:30.2718034+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": EOF"}
{"time":"2025-09-04T23:46:26.579554+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": EOF"}
{"time":"2025-09-04T23:48:15.4272271+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": EOF"}
{"time":"2025-09-04T23:58:02.7152602+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": unexpected EOF"}
{"time":"2025-09-05T00:11:37.6627513+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": EOF"}
{"time":"2025-09-05T00:12:44.2473562+08:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/xzk8559/recLSTM-bridge/g69afuga/file_stream\": unexpected EOF"}
